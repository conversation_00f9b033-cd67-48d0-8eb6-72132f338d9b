/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:41
 * @LastEditTime: 2021-12-23 22:28:42
 * @FilePath: /CardApp/src/pages/code/MaCode.js
 * love jiajia
 */
'use strict';
import React, { Component } from 'react';
import { AppRegistry, Button, StyleSheet, Text, ToastAndroid, View, Alert } from 'react-native';
import { RNCamera } from 'react-native-camera';

export default class ExampleApp extends Component {

  static navigationOptions = {
    title : "扫码验证"
  }
  
  state = {
    oper: true
  }

  showAlert () {
    Alert.alert(
      "验证码错误",
      "请确认是否为协会小程序入园码",
      [
        {
          text: "知道了",
          onPress: () => {
            this.setState({
              oper: true
            })
          },
          style: "cancel",
        },
      ]
    );
  }

  onBarCodeRead = (result) => {
    if (this.state.oper === false) {
      return false
    }
    const { data } = result; //只要拿到data就可以了
    let dataFrom = {}
    this.setState({
      oper: false
    }, async () => {
      if (Bo<PERSON>an(data)) {
        try {
          dataFrom = JSON.parse(data)
          if (!Boolean(dataFrom.txt) || !Boolean(dataFrom.end)) {
            this.showAlert()
            return false
          }
          this.setState({
            oper: true
          }, () => {
            this.props.navigation.navigate('UserInfo', {data: {
              ...dataFrom,
              way: 1
            }})
          })
        } catch(e) {
          this.showAlert()
          return false
        }
      }
    })
    
    //alert(data)
  };
  render() {
    return (
      <View style={styles.container}>
        <RNCamera
          ref={ref => {
            this.camera = ref;
          }}
          style={styles.preview}
          autoFocus={RNCamera.Constants.AutoFocus.on}/*自动对焦*/
          type={RNCamera.Constants.Type.back}
          onBarCodeRead={this.onBarCodeRead}
        >
               <View style={{
                        width: '100%',
                        height: '30%',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        position:'relative'
                    }} />
                <View style={{display: 'flex', width: '100%', height: '50%', flexDirection: 'row'}}>
                <View style={{
                        width: '15%',
                        height: '100%',
                        display:'flex',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        position:'relative'
                    }}/>
                 <View style={{
                        width: '70%',
                        height: '100%',
                        display:'flex',
                        backgroundColor: 'rgba(0,0,0,0.1)',
                        position:'relative'
                    }}/>  
                <View style={{
                        width: '15%',
                        height: '100%',
                        display:'flex',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        position:'relative'
                    }}/>
                </View>
              <View style={{
                        width: '100%',
                        height: '40%',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        position:'relative',
                        alignItems: 'center'
                    }}><Text style={{color: '#fff', paddingTop: 10}}>将一卡通二维码放入框内，即可自动扫描</Text></View>
                    
        </RNCamera>
      </View>
    );
  }

  takePicture = async () => {
    if (this.camera) {
      const options = { quality: 0.5, base64: true };
      const data = await this.camera.takePictureAsync(options);
    }
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row'
},
preview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
},
rectangleContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center'
},
rectangle: {
    height: 200,
    width: 200,
    borderWidth: 1,
    borderColor: '#fcb602',
    backgroundColor: 'transparent',
    borderRadius:10,
},
rectangleText: {
    flex: 0,
    color: '#fff',
    marginTop: 10
},
border: {
    flex: 0,
    width: 196,
    height: 2,
    backgroundColor: '#fcb602',
    borderRadius: 50
}
});