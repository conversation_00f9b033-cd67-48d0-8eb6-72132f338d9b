<component name="libraryTable">
  <library name="Gradle: com.facebook.fresco:memory-type-native:2.5.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/29ad267efc1aa56118152dd96a23ba9e/transformed/jetified-memory-type-native-2.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/29ad267efc1aa56118152dd96a23ba9e/transformed/jetified-memory-type-native-2.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/memory-type-native/2.5.0/edfa18f773d7c5cae02e680d33d66febff824e11/memory-type-native-2.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/memory-type-native/2.5.0/fa934a92fc3993f7b86da1419700fc7aa36e12f6/memory-type-native-2.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>