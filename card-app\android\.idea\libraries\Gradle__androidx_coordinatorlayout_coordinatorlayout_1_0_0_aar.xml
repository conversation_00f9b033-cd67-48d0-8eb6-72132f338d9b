<component name="libraryTable">
  <library name="Gradle: androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/be29cb4bc247544557fd2056ab72725c/transformed/coordinatorlayout-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/be29cb4bc247544557fd2056ab72725c/transformed/coordinatorlayout-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/be29cb4bc247544557fd2056ab72725c/transformed/coordinatorlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/be29cb4bc247544557fd2056ab72725c/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.coordinatorlayout/coordinatorlayout/1.0.0/4325b3c4422ad58201ed8dc15ae56bda9accedf2/coordinatorlayout-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>