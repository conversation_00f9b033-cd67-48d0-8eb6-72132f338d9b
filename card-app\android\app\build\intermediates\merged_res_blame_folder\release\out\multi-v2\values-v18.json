{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-v18\\values-v18.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}]}