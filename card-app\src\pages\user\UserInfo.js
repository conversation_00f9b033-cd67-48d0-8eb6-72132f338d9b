/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2022-08-28 18:37:21
 * @FilePath: /TOY/Users/<USER>/oneCard/CardApp/src/pages/user/UserInfo.js
 * love jiajia
 */
import React, {Component} from 'react';
import { View, Text, StyleSheet, Image, ToastAndroid, DeviceEventEmitter, Alert } from 'react-native';
import { List, Button, Flex} from '@ant-design/react-native';
import Header from '../../components/Header';
import Layout from '../../components/Layout';
import Loading from '../../components/Loading';
import Tools from '@utils/tools'
import moment from 'moment';

export default class UserInfoScreen extends Component {
  static navigationOptions = {
    headerShown : false
  }
  
  state = {
    nfc : '检测中',
    result: null,
    loading: false,
    way: 0,
  }

  async componentDidMount() {
    const data  = this.props.navigation.state.params.data
    if (!<PERSON><PERSON>an(data.txt) || !<PERSON><PERSON>an(data.end)) {
      ToastAndroid.show('验证信息错误', 2000)
      this.props.navigation.navigate('Home')
      return false
    }
    this.setState({
      loading : true,
      way: data.way || 0
    })
    const checkData = await Tools.Api('checkCard', 'POST', {
      txt: data.txt,
      end: data.end
    })
    this.setState({
      loading : false
    })
    if (checkData.code === 200) {
      this.setState({
        result : checkData.data
      })
    } else {
      this.setState({
        result : {
          code : 99,
          val: "系统错误",
          enable: false,
          user: null
        }
      })
      ToastAndroid.show(checkData.message, 2000)
    }
  }

  async operCheck(type) {
    this.setState({
      loading : true
    })
    const {result, way} = this.state
    const operData = await Tools.Api('checkAdd', 'POST', {
      user_card_code: result.user.good_id,
      type,
      check_way: way,
      book_id : result.book
    })
    this.setState({
      loading : false
    })
    if (operData.code === 200) {
      if (type === 2) { 
        this.showAlert('拒绝入园', '请向客人说明原因')
      } else {
        this.showAlert('核销成功', '请客人入园!')
      }
    } else {
      ToastAndroid.show(operData.message, 1000)
    }
  }

  showAlert (title, txt) {
    Alert.alert(
      title,
      txt,
      [
        {
          text: "知道了",
          onPress: () => {
            this.props.navigation.navigate('Home')
            DeviceEventEmitter.emit("nickBack", {});
          },
          style: "cancel",
        },
      ]
    );
  }

  render() {
    const  { result } = this.state
    if (result === null) {
      return <Layout oper={this.props}><Loading show={this.state.loading}/></Layout>
    }
    return (
      <Layout oper={this.props}>
        <Loading show={this.state.loading}/>
        <Header name="信息核验" navigation={this.props.navigation}/>
        <View style={{width: '90%', height : 80, backgroundColor: '#fff', marginLeft: '5%', marginTop: 10, borderTopLeftRadius: 10, borderTopRightRadius: 10, borderBottomRightRadius: 10, borderBottomLeftRadius: 10, padding: 10}}>
            {
              (result.code === 200 && Boolean(result.val)) ? <View>
                <View><Text style={styles.memod}>待确认</Text></View>
                <View><Text style={styles.memotxt}>{(result.val === 'ok') ? '请核实持卡人照片,确认为持卡人方可入园' : result.val.join(' , ')}</Text></View>
              </View> : null
            }
            {
              (result.code !== 200 && Boolean(result.val)) ? <View>
                  <View><Text style={styles.memo}>核验失败</Text></View>
                  <View><Text style={styles.memotxt}>{result.val}</Text></View>
                </View> : null
            }
        </View>
        {
            (result.code === 103 && Boolean(result.val)) ? <View style={{width: '90%', height : 310, backgroundColor: '#fff', marginLeft: '5%', marginTop: 10, borderTopLeftRadius: 10, borderTopRightRadius: 10, borderBottomRightRadius: 10, borderBottomLeftRadius: 10, padding: 10}}>
            <View><Text style={styles.memotips}>请持卡人扫码完成实名认证后再试</Text></View>
            <Image
              style={styles.tinyLogo}
              source={require('@static/wechat.jpg')}
            />
            </View> : null
        }
        
        {
            (result.user !== null) ?  <View style={{width: '90%', height : 360, backgroundColor: '#fff', marginLeft: '5%', marginTop: 10, borderTopLeftRadius: 10, borderTopRightRadius: 10, borderBottomRightRadius: 10, borderBottomLeftRadius: 10}}>
            <View style={{alignItems: 'center', justifyContent: 'center', marginBottom: 8, marginTop: 8, color: '#000'}}><Text>{moment().format('YYYY年MM月DD日 HH:MM:ss')}</Text></View>
            <View style={{alignItems: 'center', justifyContent: 'center', marginBottom: 20}}>
                <Image style={{width: 120, height : 120, borderWidth: 1, borderColor: '#ccc',borderTopLeftRadius: 5, borderTopRightRadius: 5, borderBottomRightRadius: 5, borderBottomLeftRadius: 5}}
              source={
                {uri: result.user.user_img}
            }/></View>
            <View style={{paddingLeft: 20, width : '90%', paddingTop: 10}}>
              <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 15}}><Text style={{color: '#999999'}}>真实姓名: </Text> {result.user.real_name}</Text></View>
              <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 15}}><Text style={{color: '#999999'}}>手机号码: </Text> {result.user.user_tel}</Text></View>
              <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 15}}><Text style={{color: '#999999'}}>证件类型: </Text> {result.user.id_card_type_str}</Text></View>
              <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 15}}><Text style={{color: '#999999'}}>证件号码: </Text> {result.user.id_card_no}</Text></View>
              <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 15}}><Text style={{color: '#999999'}}>卡片编号: </Text> {result.user.good_id}</Text></View>
              <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 15}}><Text style={{color: '#999999'}}>卡片类型: </Text> {result.user.card_name}</Text></View>
            </View>
          </View> : null
        }
        
        <View style={{alignItems: 'center', justifyContent: 'center', width: '100%', marginLeft: 10, marginBottom: 0, marginRight: 'auto', marginTop: 20}}>
          {
            (result.code === 200) ? <Flex align="start" justify='center'>
            <Flex.Item>
                <Button style={{ width: '80%', fontWeight: 'bold' ,marginLeft: 'auto', marginBottom: 0, marginRight: 'auto', marginTop: 0}}  type="primary" onPressIn={ async () => {
                await this.operCheck((result.val === 'ok') ? 0 : 1)
              }}>同意入园</Button>
            </Flex.Item>
            
            <Flex.Item>
                <Button style={{backgroundColor: '#b9b5b5', fontWeight: 'bold', borderWidth: 1, borderColor: '#cccccc', color: '#ffffff', width: '80%', marginBottom: 0, marginRight: 'auto', marginTop: 0}} type="primary" onPressIn={ async () => {
                 await this.operCheck(2)
              }}>拒绝入园</Button>
            </Flex.Item>
          </Flex> :  <View><Button style={{marginLeft: -20}} type="primary" onPressIn={() => {
                this.props.navigation.navigate('Home')
              }}>返回主菜单</Button></View>
          }
        </View>
        <View style={{color: '#666', alignItems: 'center', marginTop: 20}}><Text>{Tools.tips}</Text></View>
      </Layout>
    );
  }
}
const styles = StyleSheet.create({
  memo: {
    color: '#000000',
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    color: 'red'
  },
  tinyLogo: {
    width: 240,
    height: 240,
    marginTop: 10,
    marginBottom: 0,
    marginLeft: 'auto',
    marginRight: 'auto'
  },
  memod: {
    color: '#000000',
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    color: 'red'
  },
  memotxt: {
    textAlign: 'center',
    color: 'blue',
    fontSize: 14,
    paddingTop: 10
  },
  memotips: {
    textAlign: 'center',
    color: 'blue',
    fontSize: 16,
    paddingTop: 10,
    fontWeight: 'bold'
  },
  title : {
    color: '#000000',
    fontSize: 30,
    fontWeight: 'bold',
    paddingLeft: 20,
    paddingTop: 20,
    paddingBottom: 10
  }
});