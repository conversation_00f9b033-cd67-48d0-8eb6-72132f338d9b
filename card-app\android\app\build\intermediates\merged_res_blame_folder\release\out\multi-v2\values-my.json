{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-my\\values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "212", "endOffsets": "459"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "4081", "endColumns": "212", "endOffsets": "4289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,350,546,678,787,993,1123,1272,1407,1642,1746,1947,2088,2305,2518,2614,2713", "endColumns": "104,195,131,108,205,129,148,134,234,103,200,140,216,212,95,98,113", "endOffsets": "349,545,677,786,992,1122,1271,1406,1641,1745,1946,2087,2304,2517,2613,2712,2826"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2895,3004,3200,3336,3449,3655,3789,3942,4294,4529,4637,4838,4983,5200,5417,5517,5620", "endColumns": "108,195,135,112,205,133,152,138,234,107,200,144,216,216,99,102,117", "endOffsets": "2999,3195,3331,3444,3650,3784,3937,4076,4524,4632,4833,4978,5195,5412,5512,5615,5733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5822", "endColumns": "100", "endOffsets": "5918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,319,436,529,641,769,847,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1951,2074,2186,2288,2414,2525,2635,2795,2895", "endColumns": "108,104,116,92,111,127,77,78,90,92,95,93,100,92,94,93,90,90,85,122,111,101,125,110,109,159,99,83", "endOffsets": "209,314,431,524,636,764,842,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1946,2069,2181,2283,2409,2520,2630,2790,2890,2974"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,319,436,529,641,769,847,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1951,2074,2186,2288,2414,2525,2635,2795,5738", "endColumns": "108,104,116,92,111,127,77,78,90,92,95,93,100,92,94,93,90,90,85,122,111,101,125,110,109,159,99,83", "endOffsets": "209,314,431,524,636,764,842,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1946,2069,2181,2283,2409,2520,2630,2790,2890,5817"}}]}]}