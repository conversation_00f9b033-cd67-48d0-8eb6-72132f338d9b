{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-as\\values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,515,617,736,816,896,987,1080,1173,1268,1368,1461,1558,1652,1743,1834,1923,2025,2140,2243,2352,2471,2591,2758,2861", "endColumns": "107,98,106,95,101,118,79,79,90,92,92,94,99,92,96,93,90,90,88,101,114,102,108,118,119,166,102,88", "endOffsets": "208,307,414,510,612,731,811,891,982,1075,1168,1263,1363,1456,1553,1647,1738,1829,1918,2020,2135,2238,2347,2466,2586,2753,2856,2945"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,515,617,736,816,896,987,1080,1173,1268,1368,1461,1558,1652,1743,1834,1923,2025,2140,2243,2352,2471,2591,2758,5475", "endColumns": "107,98,106,95,101,118,79,79,90,92,92,94,99,92,96,93,90,90,88,101,114,102,108,118,119,166,102,88", "endOffsets": "208,307,414,510,612,731,811,891,982,1075,1168,1263,1363,1456,1553,1647,1738,1829,1918,2020,2135,2238,2347,2466,2586,2753,2856,5559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "184", "endOffsets": "431"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3991", "endColumns": "184", "endOffsets": "4171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,351,536,657,765,954,1074,1213,1351,1553,1660,1841,1964,2156,2336,2422,2514", "endColumns": "105,184,120,107,188,119,138,137,201,106,180,122,191,179,85,91,111", "endOffsets": "350,535,656,764,953,1073,1212,1350,1552,1659,1840,1963,2155,2335,2421,2513,2625"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2861,2971,3156,3281,3393,3582,3706,3849,4176,4378,4489,4670,4797,4989,5173,5263,5359", "endColumns": "109,184,124,111,188,123,142,141,201,110,180,126,191,183,89,95,115", "endOffsets": "2966,3151,3276,3388,3577,3701,3844,3986,4373,4484,4665,4792,4984,5168,5258,5354,5470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5564", "endColumns": "100", "endOffsets": "5660"}}]}]}