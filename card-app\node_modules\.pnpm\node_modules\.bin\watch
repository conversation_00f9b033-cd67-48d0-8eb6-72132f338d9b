#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@cnakazawa+watch@1.0.4/node_modules/@cnakazawa/watch/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@cnakazawa+watch@1.0.4/node_modules/@cnakazawa/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@cnakazawa+watch@1.0.4/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@cnakazawa+watch@1.0.4/node_modules/@cnakazawa/watch/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@cnakazawa+watch@1.0.4/node_modules/@cnakazawa/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@cnakazawa+watch@1.0.4/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@cnakazawa/watch/cli.js" "$@"
else
  exec node  "$basedir/../@cnakazawa/watch/cli.js" "$@"
fi
