<component name="libraryTable">
  <library name="Gradle: androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/b133b9404a8c5da5d70c49b2ea9a8235/transformed/slidingpanelayout-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b133b9404a8c5da5d70c49b2ea9a8235/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.slidingpanelayout/slidingpanelayout/1.0.0/f3f2e4fded24d5969a86e1974ad7e96975d970a0/slidingpanelayout-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>