{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-bs\\values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "199", "endOffsets": "446"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3992", "endColumns": "199", "endOffsets": "4187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,541,665,773,982,1106,1244,1371,1584,1689,1881,2006,2215,2387,2481,2569", "endColumns": "103,191,123,107,208,123,137,126,212,104,191,124,208,171,93,87,109", "endOffsets": "348,540,664,772,981,1105,1243,1370,1583,1688,1880,2005,2214,2386,2480,2568,2678"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2842,2950,3142,3270,3382,3591,3719,3861,4192,4405,4514,4706,4835,5044,5220,5318,5410", "endColumns": "107,191,127,111,208,127,141,130,212,108,191,128,208,175,97,91,113", "endOffsets": "2945,3137,3265,3377,3586,3714,3856,3987,4400,4509,4701,4830,5039,5215,5313,5405,5519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,333,440,526,630,752,834,915,1006,1099,1195,1289,1390,1483,1578,1677,1768,1859,1945,2048,2153,2251,2356,2469,2572,2745,2842", "endColumns": "118,108,106,85,103,121,81,80,90,92,95,93,100,92,94,98,90,90,85,102,104,97,104,112,102,172,96,83", "endOffsets": "219,328,435,521,625,747,829,910,1001,1094,1190,1284,1385,1478,1573,1672,1763,1854,1940,2043,2148,2246,2351,2464,2567,2740,2837,2921"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,333,440,526,630,752,834,915,1006,1099,1195,1289,1390,1483,1578,1677,1768,1859,1945,2048,2153,2251,2356,2469,2572,2745,5524", "endColumns": "118,108,106,85,103,121,81,80,90,92,95,93,100,92,94,98,90,90,85,102,104,97,104,112,102,172,96,83", "endOffsets": "219,328,435,521,625,747,829,910,1001,1094,1190,1284,1385,1478,1573,1672,1763,1854,1940,2043,2148,2246,2351,2464,2567,2740,2837,5603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5608", "endColumns": "100", "endOffsets": "5704"}}]}]}