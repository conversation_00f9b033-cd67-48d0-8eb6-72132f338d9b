/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2021-12-23 22:23:36
 * @FilePath: /CardApp/src/pages/code/KeyCode.js
 * love jiajia
 */
import React, {Component} from 'react';
import { View, Text, StyleSheet, Image,ToastAndroid, DeviceEventEmitter } from 'react-native';
import { Button, InputItem, List } from '@ant-design/react-native';
import Layout from '../../components/Layout';
import Header from '../../components/Header';
import Tools from '@utils/tools'
import moment from 'moment';

export default class UserInfoScreen extends Component {
  static navigationOptions = {
    headerShown : false
  }
  
  state = {
    txt : '',
    loading: false
  }

  async componentDidMount() {
  
  }

  async loginOper () {
    const { txt } =  this.state
    if (Tools.getTrim(txt) === '') {
      ToastAndroid.show('请填写正确的卡密信息', 1000) 
      return false
    }
    const data = {
      txt,
      end: moment(moment().add(10 ,'m')).unix().valueOf(),
      way: 2
    }
    this.props.navigation.navigate('UserInfo', {data})
  }


  render() {
    return (
      <Layout oper={this.props}>
        <Header name="卡密验证" navigation={this.props.navigation}/>
        <View style={styles.title}>
        <Text style={{textAlign: 'center', paddingBottom: 8}}>下图右下角激活码即为卡密</Text>
            <Image
              style={styles.tinyLogo}
              source={require('@static/card.jpg')}
            />
            
        </View>
        <View style={styles.formList}>
        <List>
          <InputItem
            clear
            value={this.state.txt}
            maxLength={10}
            onChange={value => {
              this.setState({
                txt: value
              });
            }}
            placeholder="请输入卡密"
          >
            卡密
          </InputItem>
          
          </List>
          <View style={styles.btn}>
            <Button
                onPress={() => {
                  this.loginOper()
                }}
                type="primary"
              >
                立即验证
              </Button>
            </View>
            </View>
        <View style={{color: '#666', alignItems: 'center', marginTop: 20}}><Text>{Tools.tips}</Text></View>
      </Layout>
    );
  }
}
const styles = StyleSheet.create({
  tinyLogo: {
    width: 200,
    height: 120,
    marginTop: 0,
    marginBottom: 20,
    marginLeft: 'auto',
    marginRight: 'auto'
  },  
  title : {
    marginTop: 20,
  },
  titletxt: {
    textAlign:'center',
    color: '#000000',
    fontSize: 18,
    fontWeight: 'bold'
  },
  formList: {
    width: '95%',
    paddingTop: 20,
    paddingBottom: 20,
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 'auto',
    marginRight: 'auto'
  },
  btn : {
    marginTop: 20
  }
});