#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/ua-parser-js@1.0.40/node_modules/ua-parser-js/script/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/ua-parser-js@1.0.40/node_modules/ua-parser-js/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/ua-parser-js@1.0.40/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/ua-parser-js@1.0.40/node_modules/ua-parser-js/script/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/ua-parser-js@1.0.40/node_modules/ua-parser-js/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/ua-parser-js@1.0.40/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ua-parser-js/script/cli.js" "$@"
else
  exec node  "$basedir/../ua-parser-js/script/cli.js" "$@"
fi
