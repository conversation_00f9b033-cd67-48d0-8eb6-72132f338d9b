<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":react-native-picker_picker" external.linked.project.path="$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android" external.root.project.path="$MODULE_DIR$/../../.." external.system.id="GRADLE" external.system.module.group="CardApp" external.system.module.version="unspecified" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":react-native-picker_picker" />
        <option name="LAST_SUCCESSFUL_SYNC_AGP_VERSION" value="4.2.2" />
        <option name="LAST_KNOWN_AGP_VERSION" value="4.2.2" />
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDebugSources" />
        <afterSyncTasks>
          <task>generateDebugSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/src/main/res;file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/src/debug/res;file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build/generated/res/rs/debug;file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build/generated/res/resValues/debug" />
        <option name="TEST_RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/src/androidTest/res;file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/src/androidTestDebug/res;file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build/generated/res/rs/androidTest/debug;file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build/generated/res/resValues/androidTest/debug" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
        <option name="PROJECT_TYPE" value="1" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build/intermediates/javac/debug/classes" />
    <output-test url="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build/intermediates/javac/debugUnitTest/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android">
      <excludeFolder url="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/../../../../node_modules/@react-native-picker/picker/android/build" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 30 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>