1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cardapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:4:5-67
11-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.NFC" />
12-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:5:5-62
12-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:5:22-59
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:6:5-65
13-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:6:22-62
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:7:5-70
14-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:7:22-68
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:8:5-80
15-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:8:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:9:5-81
16-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:9:22-78
17
18    <uses-feature
18-->[:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:11:5-13:36
19        android:name="android.hardware.camera"
19-->[:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:12:9-47
20        android:required="false" />
20-->[:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:13:9-33
21    <uses-feature
21-->[:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:14:5-16:36
22        android:name="android.hardware.camera.autofocus"
22-->[:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:15:9-57
23        android:required="false" />
23-->[:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:16:9-33
24    <uses-feature
24-->[:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-13:36
25        android:name="android.hardware.nfc"
25-->[:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:9-44
26        android:required="false" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
26-->[:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-33
27    <!-- <uses-sdk android:minSdkVersion="14"/> -->
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
28-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
29
30    <application
30-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:11:5-29:19
31        android:name="com.cardapp.MainApplication"
31-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:12:7-38
32        android:allowBackup="false"
32-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:16:7-34
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:24:18-86
34        android:icon="@mipmap/ic_launcher"
34-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:14:7-41
35        android:label="@string/app_name"
35-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:13:7-39
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:15:7-52
37        android:theme="@style/AppTheme" >
37-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:17:7-38
38        <activity
38-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:18:7-28:18
39            android:name="com.cardapp.MainActivity"
39-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:19:9-37
40            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
40-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:21:9-86
41            android:label="@string/app_name"
41-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:20:9-41
42            android:launchMode="singleTask"
42-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:22:9-40
43            android:windowSoftInputMode="adjustResize" >
43-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:23:9-51
44            <intent-filter>
44-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:24:9-27:25
45                <action android:name="android.intent.action.MAIN" />
45-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:25:13-65
45-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:25:21-62
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:26:13-73
47-->D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:26:23-70
48            </intent-filter>
49        </activity>
50
51        <provider
51-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-18:20
52            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
52-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-83
53            android:authorities="com.cardapp.fileprovider"
53-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:13-64
54            android:exported="false"
54-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:13-37
55            android:grantUriPermissions="true" >
55-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:13-47
56            <meta-data
56-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-17:63
57                android:name="android.support.FILE_PROVIDER_PATHS"
57-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:17-67
58                android:resource="@xml/file_provider_paths" />
58-->[:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:17-60
59        </provider>
60
61        <service
61-->[com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:9:9-15:19
62            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
62-->[com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:10:13-91
63            android:directBootAware="true"
63-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:17:13-43
64            android:exported="false" >
64-->[com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:11:13-37
65            <meta-data
65-->[com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:12:13-14:85
66                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
66-->[com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:13:17-120
67                android:value="com.google.firebase.components.ComponentRegistrar" />
67-->[com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:14:17-82
68            <meta-data
68-->[com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:12:13-14:85
69                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
69-->[com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:13:17-114
70                android:value="com.google.firebase.components.ComponentRegistrar" />
70-->[com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:14:17-82
71            <meta-data
71-->[com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:12:13-14:85
72                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
72-->[com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:13:17-114
73                android:value="com.google.firebase.components.ComponentRegistrar" />
73-->[com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:14:17-82
74            <meta-data
74-->[com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:12:13-14:85
75                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
75-->[com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:13:17-124
76                android:value="com.google.firebase.components.ComponentRegistrar" />
76-->[com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:14:17-82
77            <meta-data
77-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:20:13-22:85
78                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
78-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:21:17-120
79                android:value="com.google.firebase.components.ComponentRegistrar" />
79-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:22:17-82
80        </service>
81
82        <provider
82-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:9:9-13:38
83            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
83-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:10:13-78
84            android:authorities="com.cardapp.mlkitinitprovider"
84-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:11:13-69
85            android:exported="false"
85-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:12:13-37
86            android:initOrder="99" />
86-->[com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:13:13-35
87
88        <service
88-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
89            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
89-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
90            android:exported="false" >
90-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
91            <meta-data
91-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
92                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
92-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
93                android:value="cct" />
93-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
94        </service>
95        <service
95-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
96            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
96-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
97            android:exported="false"
97-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
98            android:permission="android.permission.BIND_JOB_SERVICE" >
98-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
99        </service>
100
101        <receiver
101-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
102            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
102-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
103            android:exported="false" />
103-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
104
105        <activity
105-->[com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:23:9-26:75
106            android:name="com.google.android.gms.common.api.GoogleApiActivity"
106-->[com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:24:13-79
107            android:exported="false"
107-->[com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:25:13-37
108            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
108-->[com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:26:13-72
109
110        <meta-data
110-->[com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:23:9-25:69
111            android:name="com.google.android.gms.version"
111-->[com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:24:13-58
112            android:value="@integer/google_play_services_version" />
112-->[com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:25:13-66
113    </application>
114
115</manifest>
