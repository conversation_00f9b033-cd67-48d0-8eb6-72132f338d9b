<component name="libraryTable">
  <library name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.0.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/f14803a315f67746b9fb71068be927be/transformed/vectordrawable-animated-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f14803a315f67746b9fb71068be927be/transformed/vectordrawable-animated-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.vectordrawable/vectordrawable-animated/1.0.0/24f92bcc89d979cd8b99ae40def4e395850a6466/vectordrawable-animated-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>