<component name="libraryTable">
  <library name="Gradle: androidx.fragment:fragment:1.3.4@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d29e2b008fda9570dcb20a45a8889feb/transformed/fragment-1.3.4/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d29e2b008fda9570dcb20a45a8889feb/transformed/fragment-1.3.4/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d29e2b008fda9570dcb20a45a8889feb/transformed/fragment-1.3.4/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d29e2b008fda9570dcb20a45a8889feb/transformed/fragment-1.3.4/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.fragment/fragment/1.3.4/7d1a4c5b4083d27dd3ec8cbc2cfff6187995e642/fragment-1.3.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>