#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules/@react-native-community/cli/build/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules/@react-native-community/cli/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules/@react-native-community/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules/@react-native-community/cli/build/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules/@react-native-community/cli/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules/@react-native-community/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@react-native-community/cli/build/bin.js" "$@"
else
  exec node  "$basedir/../@react-native-community/cli/build/bin.js" "$@"
fi
