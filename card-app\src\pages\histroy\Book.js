/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2021-12-23 18:41:34
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/pages/histroy/Book.js
 * love jiajia
 */
import React, {Component} from 'react';
import { View, Text, StyleSheet, Image, ScrollView, Dimensions } from 'react-native';
import { List, Tag, Flex} from '@ant-design/react-native';
import Layout from '../../components/Layout';
import Header from '../../components/Header';
import Loading from '../../components/Loading';
import moment from 'moment';
import Tools from '@utils/tools';
const { width, height } = Dimensions.get('window')
export default class ListScreen extends Component {
  static navigationOptions = {
    headerShown : false
  }
  
  state = {
    nfc : '检测中',
    check: 'all',
    init: [],
    loading: false,
    page: 1,
    totalPage: 0,
    total: 0,
    open: false
  }

  async componentDidMount() {
    await this.loadData()
  } 

  async loadData() {
    const { page, check, init } = this.state
    this.setState({
      loading: true
    })
    const data = await Tools.Api('bookList', 'POST', {
      type: check,
      page: {
        current: page,
        pageSize : 30
      }
    })
    this.setState({
      loading: false,
    })
    if (data.code === 200) {
      const newlist = (page === 1) ? data.data.data : [
        ...init,
        ...data.data.data
      ]
      this.setState({
        init: newlist,
        totalPage: data.data.page.totalPage,
        total: data.data.page.total,
        open : true
      })
    } else {
      ToastAndroid.show(data.message, 2000) 
    }
  }

  async tabTag (check) {
    this.setState({
      check,
      page: 1,
      totalPage: 0
    }, async () => {
      await this.loadData()
    })
  }

  async tabPage (page) {
    this.setState({
      page
    }, async () => {
      await this.loadData()
    })
  } 
  render() {
    return (
      <Layout oper={this.props}>
        <Header name="预约记录" navigation={this.props.navigation}/>
        <Loading show={this.state.loading}/>
        <View style={{display: 'flex', flexDirection:'row', marginTop: 12, marginLeft: '5%'}}>
          <Tag selected={Boolean(this.state.check === 'all')} onChange={() => this.tabTag('all')}>全部</Tag>
          <Text style={{width: 4}}></Text>
          <Tag selected={Boolean(this.state.check === 'today')} onChange={() => this.tabTag('today')}>今日</Tag>
          <Text style={{width: 4}}></Text>
          <Tag selected={Boolean(this.state.check === 'yestoday')} onChange={() => this.tabTag('yestoday')}>昨日</Tag>
          <Text style={{width: 4}}></Text>
          <Tag selected={Boolean(this.state.check === 'week')} onChange={() => this.tabTag('week')}>本周</Tag>
          <Text style={{width: 4}}></Text>
          <Tag selected={Boolean(this.state.check === 'month')} onChange={() => this.tabTag('month')}>本月</Tag>
        </View>
           <View style={styles.show}><Text>合计预约人数: <Text style={{fontWeight: 'bold', color: 'red', fontSize: 15}}>{this.state.total}</Text> 人</Text></View>
          <ScrollView style={styles.scrollView}>
            {
              this.state.init.map((v, index) => <View key={v.id} style={{width: '90%', backgroundColor: '#fff', marginLeft: '5%', marginTop: (index === 0) ? 0 : 15, paddingTop: 10, paddingBottom: 10, borderTopLeftRadius: 10, borderTopRightRadius: 10, borderBottomRightRadius: 10, borderBottomLeftRadius: 10}}>
                <Flex align="start">
                  <Flex.Item style={{lignItems: 'left', justifyContent: 'center'}}>
                    <Text style={{color: '#000', fontSize: 14, paddingLeft: 15, fontWeight: 'bold', paddingBottom: 8}}>{moment(v.add_time_unix * 1000).format('YYYY年MM月DD日')}</Text>
                  </Flex.Item>
                </Flex>
                <Flex  align="start">
                  <Flex.Item>
                    <View style={{ width : '100%', paddingTop: 5, paddingLeft: 15}}>
                      <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 12}}><Text style={{color: '#999999'}}>真实姓名: </Text> {v.real_name}</Text></View>
                      <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 12}}><Text style={{color: '#999999'}}>卡片编号: </Text> {v.user_card_code}</Text></View>
                      <View style={{paddingBottom: 6}}><Text style={{color: '#000', fontSize: 12}}><Text style={{color: '#999999'}}>卡片类型: </Text> {v.card_name}</Text></View>
                      <View style={{paddingBottom: 6}}></View>
                    </View>
                  </Flex.Item>
                </Flex>
              </View> )
            }
            {(this.state.page < this.state.totalPage) ? <View style={styles.more} onTouchEnd={async () => {
                await this.tabPage(this.state.page + 1)
            }}><Text style={{textAlign: 'center', color : '#ffffff'}}>点击加载更多</Text></View> : null}
            {
              (this.state.init.length === 0 && this.state.page === 1 && this.state.open === true) ? <View style={styles.none}>
                  <Image style={styles.tinyLogo} source={require('@static/none.png')}/>
                  <Text style={{textAlign: 'center', paddingTop: 10}}>暂无信息</Text>
              </View> : null
            }
            <View style={{color: '#cccccc', alignItems: 'center', marginTop: 20}}><Text>{Tools.tips}</Text></View>
            <View style={{height: 20}}></View>
          </ScrollView>
        
      </Layout>
    );
  }
}
const styles = StyleSheet.create({
  show : {
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 24
  },  
  more : {
    width: '90%',
    height: 40,
    backgroundColor: '#5468ff',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 0,
    marginLeft: 'auto',
    marginRight: 'auto',
    borderRadius: 10
  },
  container: {
    flex: 1,
    height: 500,
  },
  scrollView : {
    height: height - 140,
    marginHorizontal: 2,
  },
  tinyLogo : {
    width: 40,
    height: 40,
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 'auto',
    marginRight: 'auto'
  },
  title : {
    color: '#000000',
    fontSize: 30,
    fontWeight: 'bold',
    paddingLeft: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  tag : {
    display: 'flex', 
    width: 40, 
    borderWidth: 1,
    textAlign: 'center',
    color: 'green',
    borderColor: 'green',
    fontSize: 12,
    borderRadius: 4,
    justifyContent: 'center'
  },
  tagT: {
    display: 'flex', 
    width: 40, 
    borderWidth: 1,
    textAlign: 'center',
    color: 'red',
    borderColor: 'red',
    fontSize: 12,
    borderRadius: 4,
    justifyContent: 'center'
  },
  none : {
    padding: 10,
    marginTop: 30
  }
});