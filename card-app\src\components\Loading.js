/*
 * @Author: 高超
 * @Date: 2021-12-21 22:52:46
 * @LastEditTime: 2021-12-22 11:16:21
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/components/Loading.js
 * love jiajia
 */

import React, { Component } from 'react';
import { StyleSheet, Text, View, ActivityIndicator, Dimensions } from 'react-native';
const { width, height } = Dimensions.get('window')
const  Loading = (props) => {
        if (props.show) {
            return (
                <View>
                  <View style={styles.LoadingPage}>
                      <View style={{
                          width: 100,
                          height: 100,
                          backgroundColor: "rgba(0,0,0,0.6)",
                          opacity: 1,
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius:7
                      }}>
                          <ActivityIndicator size="large" color="#FFF" />
                          <Text style={{ marginLeft: 10,color:"#FFF",marginTop:10 }}>正在加载...</Text>
                      </View>
                  </View>
                </View>
            );
        } else {
            return <View/>
        }
}
export default Loading;
const styles = StyleSheet.create({
    LoadingPage: {
        position: "absolute",
        left: 0,
        top: 0,
        backgroundColor: "rgba(0,0,0,0)",
        width: width,
        height: height,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 100
    }
});