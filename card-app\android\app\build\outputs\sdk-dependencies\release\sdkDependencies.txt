# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "react-native"
    version: "0.66.2"
  }
  digests {
    sha256: "\366Y\302,Q;\271\025\034{\022\360G\377\236>\a\305\305\361\3266\034\361^\316\335\256eCbu"
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
}
library {
  maven_library {
    groupId: "com.facebook.yoga"
    artifactId: "proguard-annotations"
    version: "1.19.0"
  }
  digests {
    sha256: "\373e\367\006\356\240[V@\373k\223\322\035lA\371\342\264\347\246\346\331\362\214\351\322\245tGI\225"
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.0.2"
  }
  digests {
    sha256: "\243\b\f\335^\\V\313r\371\324(\261e}C\200\001\036\302\021\317\355\367n\bK\225\366\277\r\003"
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.1.0"
  }
  digests {
    sha256: "g\316\270&|\'EW\303WX\353\276D\211\n :Uy\275+\bM\363Mpv<r\370\017"
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fresco"
    version: "2.5.0"
  }
  digests {
    sha256: "\003\3322!6\362\340\r\373p02^\315\3430\332_Hs#\313\203w\246\002\b\205$\240\\\b"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-okhttp3"
    version: "2.5.0"
  }
  digests {
    sha256: "\n\2112cU|M\312KEk6T}&t\301v\225\201f\r\316\021\333\260@q\b7\306\t"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-common"
    version: "2.5.0"
  }
  digests {
    sha256: "N\333\242\325\244\373\333\206/\330\235!\016d\006\003\324\254\341\3745\323\240\3309\361n{\360\245\"-"
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "soloader"
    version: "0.10.1"
  }
  digests {
    sha256: "\016\n\202\370P/\201\240\240\177\221\301/\a\354TR.^\244\205\022`Lj$\237U\363I\tW"
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.9.1"
  }
  digests {
    sha256: "j\375\330\363_N\266\r\371e\302\220\372:\317)D?\251\206TQ\023\320r\233\204a\366W\037\217"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.9.1"
  }
  digests {
    sha256: "\300\246[\260?\2041<bg\035\t\333\322<K\240\305\b-w\257\310l\201:\270l?\244\000a"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.9.0"
  }
  digests {
    sha256: "\271%\\\026;~\334\v\204\006U\235fW\234l2\336\240\037i\031C\272\305\323\375\275\020\366\233D"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fbjni"
    artifactId: "fbjni-java-only"
    version: "0.2.2"
  }
  digests {
    sha256: "y\004\341\302p\374.\372\372r2\000\037\275Z\333\005!\366$\356:nT\215\036\373\313_0\274\337"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.1.0"
  }
  digests {
    sha256: "\323\215c\355\263\017\024g\201\215P\252\360_\212i-\352\21319*\004\233\372\231\033\025\232\325\266\222"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.2.0"
  }
  digests {
    sha256: "RK\213\210\316\266\247J~D\346\265g\2415f\017!\027\231\220L\262\030\277\356[\341\026h \262"
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.1.0"
  }
  digests {
    sha256: "\241L\213\217!S\361(\350\000\373\322f\246\276\253\034(9\202\242\236\305p\322\314\005\323\a\330\024\226"
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.0.1"
  }
  digests {
    sha256: "L\243X\225{\225\020\345/\303\210\340\034\2353\302\326U\324\006\277\346\347\031\204\351\257\352\237q^\322"
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.0.0"
  }
  digests {
    sha256: "&\303\240\317\n\232\232}#Z\v\000\362\363~C\035R\331\225\'Q\343\353|\220\264\265,#l\361"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.1.0"
  }
  digests {
    sha256: "\345\0278\227\271e\350pe\036\203\331\325\257\027B\323\3652\325\210c\":9\f\343\241\224\3101+"
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.0"
  }
  digests {
    sha256: "\232\035w\024\n\302\"\267\206kPT\356}\025\233\301\200\t\207\355-F\335j\375\321E\253\267\020\301"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.1.0"
  }
  digests {
    sha256: "v\333k\3453\275s\017\263a\302\376\261*,&\331\225($thG\332\202`\036\370\037\b&C"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.0.0"
  }
  digests {
    sha256: "\310&\t\316\330\304\230\360\247\001\243\017\266w\033\267H\b`\332\356\204\330.\n\201\356\206\355\367\2729"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.1.0"
  }
  digests {
    sha256: "\272U\373z\301\262\202\215S\'\315\250\254\367\b]\231\v+LC\3573l\252ghbI\270R="
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.0.0"
  }
  digests {
    sha256: "\207\346_\307g\307\022\2647d\234|\356$1\353\264\276\326\332\357\202\345\001\324\022[>\323\366_\216"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.0.0"
  }
  digests {
    sha256: "\375\3434\354~\"tL\017[\376|\257\032\204\311\327\0272pD@\005w\275\371\275\222\036\304\367\274"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.0.0"
  }
  digests {
    sha256: "\321\274\230BE\\.SD\025\330\214D\337MRA;G\215\271\t:\033\243c$\367\005\364L="
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.0.0"
  }
  digests {
    sha256: "%\020\245a\2347W\234\234\341\240Et\372\2572<\320\377\342\374N \372\217\217\001\345\273@.\203"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\345\b\306\225H\224\2237M\224+\367\264\356\002\253\367W\035%\252\304\306\"\345}l\325\315)\353s"
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "soloader"
    version: "2.5.0"
  }
  digests {
    sha256: "\242_\377\307T7\357\2346\276$\215\360K\023q\344\260\022)\371D\001Oul^\030,Q\232\202"
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "nativeloader"
    version: "0.10.1"
  }
  digests {
    sha256: "\202\312\2344\217`Y\204\372r\340\212\356\f5\347\252\020>\306\260\354\317Y.I\203\222\304\326\001\270"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fbcore"
    version: "2.5.0"
  }
  digests {
    sha256: "\247\204bk\a\212qY\357\366\276(\377\351jW\347a\227[\254\345\225e\240\325u{\266\314\274\304"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "drawee"
    version: "2.5.0"
  }
  digests {
    sha256: "\344\315\035\312\207y;\350P \274\220\203P|\351\271*\377\274\211\210-vt\300\370;\240F\360\336"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline"
    version: "2.5.0"
  }
  digests {
    sha256: "\242\327\t,\233\302\025\001Y\316{\347\227\355\366\252\300\246\221<\345\253\213\002I!o\375F\267S\'"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-native"
    version: "2.5.0"
  }
  digests {
    sha256: "E@\227g%\000\335\344\003\300\2154Z\331k0\021\254u_\270\017\2557/\242\270\350*\263\357\352"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-ashmem"
    version: "2.5.0"
  }
  digests {
    sha256: "\355\200\304 \030\304\3751\222\030\274#\226\343\213\'3{T\034E\301\357\352\253\316\266\302\254\341\367\350"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-native"
    version: "2.5.0"
  }
  digests {
    sha256: "\253X\207\274\201\262\0025 Jz;\006\0060\324\352f\216\366\377\253\361\352\247\231\307j\263\312)\335"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-java"
    version: "2.5.0"
  }
  digests {
    sha256: "\213\fi2\247\213\025\367[\217\024\3405\330?\370b\033\204\376{3$ZM5|\032\245\253\257\220"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagefilters"
    version: "2.5.0"
  }
  digests {
    sha256: "\200\310\314\f\375\231\240\362ns}\t\307\251\272m\fI\315\300\v\342\334\366\255f\255\027K\2743\374"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagetranscoder"
    version: "2.5.0"
  }
  digests {
    sha256: "\265\031H\354*\256s\253\3527\315\376\205F\037\362\304\263\322\\\242+\023\256\212V+\203&\304%R"
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "annotation"
    version: "0.10.1"
  }
  digests {
    sha256: "\255\f\207\256\367\002\233d\311\364\177\320\220K\024;-]\005\364\224S\262\370\001\2425\031C\261\324\353"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "middleware"
    version: "2.5.0"
  }
  digests {
    sha256: "\004\2219\232\0375\026S\207R\027\245\000\343\261\3435n\251{\a\233\321#\345\212,p\305\264J\024"
  }
}
library {
  maven_library {
    groupId: "com.parse.bolts"
    artifactId: "bolts-tasks"
    version: "1.4.0"
  }
  digests {
    sha256: "\233\305\036>\312\205:\235\362\373\254b\3021}\262\311\352a\233\225\265S\0340D\002C\332\254\316\342"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\223\306\320\277\315\216\271Y(\221\361\263*C\024\251\202\362%\020\337D\2331q.\227#\320\227\265\355"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.5.30"
  }
  digests {
    sha256: "\305V\b\351\353m\3672~t\262\036\'\0352M\305#\316\363\025\207\270\326\3229=\260\215n\000\f"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.5.30"
  }
  digests {
    sha256: "\216\263\254S\n\227\204\"\340\370\320\273\247\213\242\306(\276\311\227\334/\032\244\357\214[\205N7d\270"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.4.10"
  }
  digests {
    sha256: "9\267\251D-z8e\340\364\2472\305l\035]\240\341\037\373;\270*F\0352\336\260\300\312vs"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.4.10"
  }
  digests {
    sha256: "\371Vc\200\300\207\"\307\200\3163\316\356#\351\215\337v\\\251\217\253\323\342\372\272\347\227\\\215#+"
  }
}
library {
  maven_library {
    groupId: "org.webkit"
    artifactId: "android-jsc"
    version: "r250230"
  }
  digests {
    sha256: "MW#\304\216\321?\273C\326\021|\207\372\206bz\371\224\276\037\3763|>\026\025f\220\260Ux"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-barcode-scanning"
    version: "16.2.0"
  }
  digests {
    sha256: ",\323s\204\361\234\353O\370\223fh\326.\"|\264\231\256\024JD\a\365\242\b\340\356\272\247\t\271"
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "2.2.1"
  }
  digests {
    sha256: "\346/s\272\034\177]\025\365\330m\216\232)\214B\314\320d\216|\346\v\2361/.\315\022;^\320"
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "2.3.3"
  }
  digests {
    sha256: "\262*\024\325`\245\220\334\207^\220$$\262\232\023\240-\037\257){|9\251\000yg\377\255\314\275"
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "2.2.6"
  }
  digests {
    sha256: "\330:\322<\023D)i\021a\311$v\243\310=\3626\315\037\213\255\277\324\360\a#\264\354\315t\034"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "17.6.0"
  }
  digests {
    sha256: "$n\302\272\361m\r*!\026M\361\030\377Q\\\r\305\304U\333\034\027\223\326\002\272\371\005\233J\377"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "17.6.0"
  }
  digests {
    sha256: "\'\312aG\256\240\253\016\242 \202\006\006\017\277\206\201\025N\322T\250\322W:\351\246\360&\212pM"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "17.2.1"
  }
  digests {
    sha256: "\272T\026Z\371\f\312\215#\245\330\300\235E\211\225`\356\022\204=WRo\322W<\201\316\272WE"
  }
}
library {
  maven_library {
    groupId: "com.google.android.odml"
    artifactId: "image"
    version: "1.0.0-beta1"
  }
  digests {
    sha256: ".q\2521\370:\224\025\'\177\021\235\346q\225ro\a\321v\016\225B\301\021w\2142\016:\241\362"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "16.1.0"
  }
  digests {
    sha256: "\202\351\032\245\355\030n\355\257\037\"\020\365\322A\272nDV\317\f2\224\235\\\333\341R\260\342\350\213"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "16.1.0"
  }
  digests {
    sha256: "\217\211\247B\230\273\314\302\305C8R\b\242\360\306\177`<\346~n\302\034\307r\351\216h\217|\365"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "17.1.0"
  }
  digests {
    sha256: "\202\225\307U\274\255\310z*\256\345\271\345h\362\177\3218\375E\377\021Y\250\356t|\364\303\243\215\205"
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "common"
    version: "17.2.0"
  }
  digests {
    sha256: "\223\334\223\362\263\262\215\324\374k\2468\363\377\'\"\0042\033(EV\221}\345\264\263\362Xc:0"
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-common"
    version: "16.5.0"
  }
  digests {
    sha256: "\334\376]3\313\353\004\200Cm\\mB\220/\2478\241c\353l\n\312\260\201\211\\\220\204\031W\n"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.0.0"
  }
  digests {
    sha256: "\2100\362\350\245\3444\313\305\344\275\030\0230s\266\350Q\212\214\350\354,\346\363\262s\311\035\226d\302"
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.2"
  }
  digests {
    sha256: "\207p\301\200\020>\v\214\004\240~\264\305\221S\257c\233\t\354\242]\352\351\275\315\257\206\235\036[k"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-face-detection"
    version: "16.2.0"
  }
  digests {
    sha256: "o\335\271\211\266\r\273v\325\210\365\252=d\231\237\\q\233\207\264\247\340F\257\250xH>\216v\006"
  }
}
library {
  maven_library {
    groupId: "com.google.zxing"
    artifactId: "core"
    version: "3.3.3"
  }
  digests {
    sha256: "X \370\036\224>K\316\003)0f!\342\326%])0\260\246\316\223L\\#\300\326\323\362\005\231"
  }
}
library {
  maven_library {
    groupId: "com.drewnoakes"
    artifactId: "metadata-extractor"
    version: "2.11.0"
  }
  digests {
    sha256: "\365\354V\306\260\032\373\375p\031\342\332s\275\354]\"\306\rb\f\016\200C\346\250Z\333UM\r\367"
  }
}
library {
  maven_library {
    groupId: "com.adobe.xmp"
    artifactId: "xmpcore"
    version: "5.1.3"
  }
  digests {
    sha256: "\202\033\351\a\361\345\024\353\265\017\f\240K,\t\203p\243\313^_\235\334\302\354\370\036s\353&]\252"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-text-recognition"
    version: "16.3.0"
  }
  digests {
    sha256: "\334=~\031\361\177*\226T\020\214\254\236\305\370\346\320)e]i\274=I\362\315\337\227ov\311\315"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.4.0"
  }
  digests {
    sha256: "\032\021\262\n\254\023R\336r\332Sf\267f\202\316\213\246\271\260\276\361jv\273=\364\032\235\270\221\227"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 1
  library_dep_index: 11
  library_dep_index: 16
}
library_dependencies {
  library_index: 4
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 18
  library_dep_index: 17
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 19
}
library_dependencies {
  library_index: 25
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
}
library_dependencies {
  library_index: 27
  library_dep_index: 17
}
library_dependencies {
  library_index: 28
  library_dep_index: 17
}
library_dependencies {
  library_index: 26
  library_dep_index: 17
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 17
}
library_dependencies {
  library_index: 20
  library_dep_index: 17
}
library_dependencies {
  library_index: 21
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 29
  library_dep_index: 17
}
library_dependencies {
  library_index: 30
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 33
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 28
}
library_dependencies {
  library_index: 35
  library_dep_index: 17
  library_dep_index: 28
}
library_dependencies {
  library_index: 36
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 35
}
library_dependencies {
  library_index: 34
  library_dep_index: 17
}
library_dependencies {
  library_index: 31
  library_dep_index: 17
}
library_dependencies {
  library_index: 32
  library_dep_index: 17
}
library_dependencies {
  library_index: 22
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 37
  library_dep_index: 30
  library_dep_index: 38
  library_dep_index: 34
}
library_dependencies {
  library_index: 37
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 38
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 25
  library_dep_index: 34
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 17
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 23
  library_dep_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 24
  library_dep_index: 23
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 21
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 6
  library_dep_index: 46
  library_dep_index: 20
}
library_dependencies {
  library_index: 42
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 39
}
library_dependencies {
  library_index: 43
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 39
}
library_dependencies {
  library_index: 44
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 39
}
library_dependencies {
  library_index: 45
  library_dep_index: 17
}
library_dependencies {
  library_index: 6
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 45
}
library_dependencies {
  library_index: 46
  library_dep_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 5
  library_dep_index: 18
}
library_dependencies {
  library_index: 7
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 9
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 47
  library_dep_index: 49
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 58
  library_dep_index: 48
}
library_dependencies {
  library_index: 9
  library_dep_index: 49
}
library_dependencies {
  library_index: 50
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 9
  library_dep_index: 59
}
library_dependencies {
  library_index: 51
  library_dep_index: 48
  library_dep_index: 58
  library_dep_index: 60
  library_dep_index: 49
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
  library_dep_index: 58
  library_dep_index: 60
  library_dep_index: 49
}
library_dependencies {
  library_index: 52
  library_dep_index: 51
  library_dep_index: 49
  library_dep_index: 10
}
library_dependencies {
  library_index: 53
  library_dep_index: 49
  library_dep_index: 51
}
library_dependencies {
  library_index: 54
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 48
}
library_dependencies {
  library_index: 55
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 59
  library_dep_index: 49
  library_dep_index: 9
}
library_dependencies {
  library_index: 56
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 48
  library_dep_index: 60
  library_dep_index: 49
}
library_dependencies {
  library_index: 57
  library_dep_index: 61
  library_dep_index: 48
  library_dep_index: 60
  library_dep_index: 49
}
library_dependencies {
  library_index: 8
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 14
  library_dep_index: 62
}
library_dependencies {
  library_index: 14
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 62
  library_dep_index: 64
  library_dep_index: 63
}
library_dependencies {
  library_index: 13
  library_dep_index: 12
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 62
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 62
}
library_dependencies {
  library_index: 15
  library_dep_index: 48
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 69
  library_dep_index: 17
}
library_dependencies {
  library_index: 70
  library_dep_index: 17
  library_dep_index: 69
  library_dep_index: 71
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 71
  library_dep_index: 17
  library_dep_index: 69
  library_dep_index: 3
}
library_dependencies {
  library_index: 77
  library_dep_index: 17
}
library_dependencies {
  library_index: 78
  library_dep_index: 17
  library_dep_index: 77
}
library_dependencies {
  library_index: 72
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 22
  library_dep_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 73
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 22
}
library_dependencies {
  library_index: 74
  library_dep_index: 73
}
library_dependencies {
  library_index: 76
  library_dep_index: 17
  library_dep_index: 81
}
library_dependencies {
  library_index: 79
  library_dep_index: 18
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 80
  library_dep_index: 82
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 82
  library_dep_index: 17
}
library_dependencies {
  library_index: 83
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 87
  library_dep_index: 18
  library_dep_index: 88
  library_dep_index: 21
  library_dep_index: 41
  library_dep_index: 22
}
library_dependencies {
  library_index: 88
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 26
}
library_dependencies {
  library_index: 89
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 90
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 91
  library_dep_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 91
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 39
  library_dep_index: 19
}
library_dependencies {
  library_index: 92
  library_dep_index: 17
  library_dep_index: 18
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 67
  dependency_index: 6
}
