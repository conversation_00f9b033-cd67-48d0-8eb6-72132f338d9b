<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-pager-view" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\react-native-pager-view\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-nfc-manager" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\library_assets\generalRelease\out"/></dataSet><dataSet config=":react-native-picker_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\@react-native-picker\picker\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-community_masked-view" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\@react-native-community\masked-view\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-community_cameraroll" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\@react-native-community\cameraroll\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\android\app\src\main\assets"><file name="fonts/antfill.ttf" path="D:\Sites\card-all\card-app\android\app\src\main\assets\fonts\antfill.ttf"/><file name="fonts/antoutline.ttf" path="D:\Sites\card-all\card-app\android\app\src\main\assets\fonts\antoutline.ttf"/><file name="index.android.bundle" path="D:\Sites\card-all\card-app\android\app\src\main\assets\index.android.bundle"/></source><source path="D:\Sites\card-all\card-app\android\app\build\intermediates\shader_assets\release\out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Sites\card-all\card-app\android\app\src\release\assets"/></dataSet></merger>