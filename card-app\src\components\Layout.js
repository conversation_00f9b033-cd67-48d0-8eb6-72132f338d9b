/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2021-12-23 00:27:29
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/components/Layout.js
 * love jiajia
 */
import React, {Component} from 'react';
import { View } from 'react-native';
import { BackHandler, ToastAndroid } from 'react-native';
import Tools from '@utils/tools';

export default class ListScreen extends Component {

  static navigationOptions = {
    title : "loading..."
  }
  
  async componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    const token = await Tools.getData('token')
    //await Tools.setData('token', null)
    if (this.props.oper.navigation.state.routeName !== 'Login' && token === null) {
      this.props.oper.navigation.navigate('Login')
    }
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress',
      this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => {
    if (this.props.oper.navigation.isFocused()) {
        if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
            BackHandler.exitApp();
            return false;
        }
        this.lastBackPressed = Date.now();
        ToastAndroid.show('再按一次退出应用', ToastAndroid.SHORT);
        return true;
    }
  }

  render() {
    return (
      <View>
        {this.props.children}
      </View>
    );
  }
}