/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2021-12-23 18:50:35
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/pages/login/Login.js
 * love jiajia
 */
import React, {Component} from 'react';
import { View, Text, StyleSheet, Image,ToastAndroid, DeviceEventEmitter } from 'react-native';
import { Button, InputItem, List } from '@ant-design/react-native';
import Loading from '../../components/Loading';
import Layout from '../../components/Layout';
import Tools from '@utils/tools'
import moment from 'moment';
const Item = List.Item;

export default class UserInfoScreen extends Component {
  static navigationOptions = {
    headerShown : false
  }
  
  state = {
    check_name : '',
    check_password: '',
    loading: false
  }

  async componentDidMount() {
    //console.log(await Tools.getData('token'))
    await this.loginCheck()
    this.listenerHeaderBack = DeviceEventEmitter.addListener("hardwareBackPress",  async (e) => {
      await this.loginCheck()
    });
  }

  componentWillUnmount() {
    this.listenerHeaderBack.remove();
  }

  async loginCheck() {
    const token = await Tools.getData('token')
    if (token !== null) {
      this.props.navigation.navigate('Home')
    }
  }

  async loginOper () {
    const { check_name, check_password, loading } =  this.state
    if (loading) { return false}
    if (Tools.getTrim(check_name) === '' || Tools.getTrim(check_password) === '') {
      ToastAndroid.show('请填写正确的登录信息', 1000) 
      return false
    }
    this.setState({
      loading: true
    })
    const data = await Tools.Api('checkLogin', 'POST', {
      check_name,
      check_password
    })
    if (data.code !== 200) {
      this.setState({
        loading: false
      })
      ToastAndroid.show(data.message, 2000) 
    } else {
      await Tools.setData('token', data.data.token)
      this.setState({
        loading: false
      })
      this.props.navigation.navigate('Home')
      DeviceEventEmitter.emit("nickBack", {});
    }
  }

  render() {
    return (
      <View>
        <Loading show={this.state.loading}/>
        <View style={styles.title}>
        <Image
              style={styles.tinyLogo}
              source={require('@static/logo.png')}
            />
          <Text style={styles.titletxt}>北京风景名胜协会验证终端</Text>
        </View>
        <View style={styles.formList}>
        <List>
          <InputItem
            clear
            value={this.state.check_name}
            maxLength={18}
            onChange={value => {
              this.setState({
                check_name: value
              });
            }}
            placeholder="请输入用户名"
          >
            用户名
          </InputItem>
          <InputItem
            clear
            type="password"
            maxLength={10}
            value={this.state.check_password}
            onChange={value => {
              this.setState({
                check_password: value
              });
            }}
            placeholder="请输入密码"
          >
            密码
          </InputItem>
          </List>
          <View style={styles.btn}>
            <Button
                onPress={() => {
                  this.loginOper()
                }}
                type="primary"
              >
                立即登录
              </Button>
            </View>
            </View>
        <View style={{color: '#666', alignItems: 'center', marginTop: 20}}><Text>{Tools.tips}</Text></View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  tinyLogo: {
    width: 70,
    height:70,
    marginTop: 0,
    marginBottom: 20,
    marginLeft: 'auto',
    marginRight: 'auto'
  },  
  title : {
    marginTop: 50,
    paddingTop: 20,
    paddingBottom: 20,
  },
  titletxt: {
    textAlign:'center',
    color: '#000000',
    fontSize: 18,
    fontWeight: 'bold'
  },
  formList: {
    width: '95%',
    paddingTop: 20,
    paddingBottom: 20,
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 'auto',
    marginRight: 'auto'
  },
  btn : {
    marginTop: 20
  }
});