version: 2.1

orbs:
  react-native: react-native-community/react-native@4.4.0

commands:
  checkout-attach-workspace:
    description: "Checkout and attach workspace"
    steps:
      - checkout
      - attach_workspace:
            at: .

jobs:
  install:
    executor: react-native/linux_js
    steps:
        - checkout-attach-workspace
        - react-native/yarn_install
        - persist_to_workspace:
            root: .
            paths:
              - node_modules

  lint:
    executor: react-native/linux_android
    steps:
      - checkout-attach-workspace
      - run:
          name: Lint
          command: yarn lint

  build-app:
    executor: react-native/linux_android
    steps:
      - checkout-attach-workspace
      - run:
          name: Run Checks
          command: |
            cd android
            chmod +x ./gradlew && ./gradlew check
      - store_artifacts:
          path: android/build/reports
      - run:
          name: Run Yarn to Generate react.gradle
          command: cd examples/basic/android && yarn
      - react-native/android_build:
          project_path: ./examples/basic/android
  
  deploy-docs:
    executor: react-native/linux_js
    steps:
      - checkout-attach-workspace
      - run:
          name: Deploying to GitHub Pages
          command: |
            git config --global user.email "${GH_EMAIL}@users.noreply.github.com"
            git config --global user.name "${GH_NAME}"
            echo "machine github.com login $GH_NAME password $GH_TOKEN_DOCS" > ~/.netrc
            cd website && yarn install && GIT_USER=${GH_NAME} yarn run publish-gh-pages

  publish-version:
    executor: react-native/linux_js
    steps:
      - checkout-attach-workspace
      - run:
          name: Run semantic-release
          command: yarn ci:publish

workflows:
  version: 2

  develop:
    jobs:
      - install:
          filters:
            branches:
              ignore: master
      
      - lint:
          requires:
            - install  

      - build-app:
          requires:
            - install  
  
  release:
    jobs:
      - install:
          filters:
            branches:
              only: master
  
      - deploy-docs:
          requires:
            - install

      - publish-version:
          requires:
            - install


