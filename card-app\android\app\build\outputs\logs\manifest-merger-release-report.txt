-- Merging decision tree log ---
manifest
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
MERGED from [:react-native-async-storage_async-storage] D:\Sites\card-all\card-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-community_cameraroll] D:\Sites\card-all\card-app\node_modules\@react-native-community\cameraroll\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-community_masked-view] D:\Sites\card-all\card-app\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-community_slider] D:\Sites\card-all\card-app\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-picker_picker] D:\Sites\card-all\card-app\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:2:1-18:12
MERGED from [:react-native-gesture-handler] D:\Sites\card-all\card-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-15:12
MERGED from [:react-native-pager-view] D:\Sites\card-all\card-app\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-safe-area-context] D:\Sites\card-all\card-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-21:12
MERGED from [com.facebook.react:react-native:0.66.2] C:\Users\<USER>\.gradle\caches\transforms-3\a21ceb2dee016b3bc178279e85108ab3\transformed\jetified-react-native-0.66.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67dc7ae83599c50a17d333e48b750491\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\8bb9561c589f5cd8f762c133b8ca94b9\transformed\appcompat-1.0.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f14803a315f67746b9fb71068be927be\transformed\vectordrawable-animated-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7fccd1f19787d63b03ada961c1fccf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9341f36f0ce470d4b55127e33c2ce075\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.webkit:android-jsc:r250230] C:\Users\<USER>\.gradle\caches\transforms-3\7873a5158addb5bf75ec8bc5226b9f2a\transformed\jetified-android-jsc-r250230\AndroidManifest.xml:2:1-11:12
MERGED from [com.facebook.fresco:fresco:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e3be5b59ff1001e620058abe4b7c8c9\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d576f05ff2510ef857a4a4bda541865\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\da58426860718cf7cd9b6432bdc1e74c\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfcd70de602582dafbe585f453d01685\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29ad267efc1aa56118152dd96a23ba9e\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfe2fe1266f7b508fab117b793747328\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\78fb03b70f57e7a893ea3d79f69454fd\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\9429e5f71025a78d24a47489563f8c07\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5526495f296c8eeaa211326006983f13\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c9b14e62e6566be253296541a1de4cb\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a48e403d00b0b0ddc92ec5c061189973\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\be9076cd4fc3ff6ad98aa6fb277b3148\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:middleware:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bd3d214b93dcf428cfd42cc8814b2ed\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\78eba43129e0d31bec1d131435c69dcf\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:soloader:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6d259317e4a56b55ba587f613fb48a2\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c346baed8905fc786ccf697aebc99f4\transformed\jetified-soloader-0.10.1\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\db0f9dfc2cb9e58d20874cad38a46146\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\e81715fd3e32bf93fae732ab0d910abb\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a19e52b53eb437c1a1e2beb909f23bc9\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\be421ef5c9cb65032068b6cd3865fbe1\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be91775d24cf865decfe7ed4d5eda66\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0c159f2eff86f9dd9484771b447df49\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a91b8843137aadf2218f91eb02c85509\transformed\vectordrawable-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b761a92b9243332432b2b44741790500\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\866aad59c867e35bbba666bca4ca6ba8\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a26264be0cc8c8203d10219c4bd7f503\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\945f679d5066ec5cea4c418de5c0d6c7\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-tasks:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c40538da6b092d65991aa98428bae53c\transformed\jetified-play-services-tasks-17.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\86fd6928850ae509fbe2c21ac2bd3ae8\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1b8018dc60702fe7502f85988cad1c2\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60d60c30d7e19309c125baad97767572\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\09064782b8f9c0d066bcd2a3c8b04255\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29cb4bc247544557fd2056ab72725c\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa7b39b5df705c5310dc54c9428ddb2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b133b9404a8c5da5d70c49b2ea9a8235\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52776be0cd260004f7c3543526e82dde\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e78fd681fe13beced154580660d7b18a\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e66c2c7de81d8a94347e1fb5ab7df0ea\transformed\jetified-activity-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b99f44f0bffbc60250191bc76f427b6\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43774a1f0f00442e6ac94bdb87895112\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\207c511f44f601b53084c2139b40be16\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1dbf150c803dbbe2473340e72042a7f\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b855ee38784b14b3dafd9751524a62d\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6648bd14adbde7c30b9a5080863e29b0\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef1333f51a0619e2cf1dea9c6bced6ac\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52571f97fa6c48622e09021afd8f199b\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\924aaf4fe951573165fc3f7358be4779\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f9e87f36900fbd3992a3b013c7da9cb\transformed\core-runtime-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:fbcore:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80cae4052e041bdf30fb0fa272e54409\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\413d0ac1b6d1bcad01a3ffa6f288985d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
	package
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:2:3-24
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:1-30:12
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.NFC
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:5:5-62
MERGED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-62
MERGED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-62
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:5:22-59
uses-permission#android.permission.CAMERA
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:9:5-65
MERGED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:9:5-65
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:7:5-70
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:9:22-78
application
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:11:5-29:19
MERGED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-19:19
MERGED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-19:19
MERGED from [com.facebook.react:react-native:0.66.2] C:\Users\<USER>\.gradle\caches\transforms-3\a21ceb2dee016b3bc178279e85108ab3\transformed\jetified-react-native-0.66.2\AndroidManifest.xml:9:5-20
MERGED from [com.facebook.react:react-native:0.66.2] C:\Users\<USER>\.gradle\caches\transforms-3\a21ceb2dee016b3bc178279e85108ab3\transformed\jetified-react-native-0.66.2\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c346baed8905fc786ccf697aebc99f4\transformed\jetified-soloader-0.10.1\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.soloader:soloader:0.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c346baed8905fc786ccf697aebc99f4\transformed\jetified-soloader-0.10.1\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-tasks:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c40538da6b092d65991aa98428bae53c\transformed\jetified-play-services-tasks-17.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c40538da6b092d65991aa98428bae53c\transformed\jetified-play-services-tasks-17.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b99f44f0bffbc60250191bc76f427b6\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b99f44f0bffbc60250191bc76f427b6\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\413d0ac1b6d1bcad01a3ffa6f288985d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\413d0ac1b6d1bcad01a3ffa6f288985d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:appComponentFactory
		ADDED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:13:7-39
	android:roundIcon
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:15:7-52
	android:icon
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:14:7-41
	android:allowBackup
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:16:7-34
	android:theme
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:17:7-38
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:12:7-38
activity#com.cardapp.MainActivity
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:18:7-28:18
	android:label
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:20:9-41
	android:launchMode
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:22:9-40
	android:windowSoftInputMode
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:23:9-51
	android:configChanges
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:21:9-86
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:19:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:24:9-27:25
action#android.intent.action.MAIN
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:25:13-65
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:25:21-62
category#android.intent.category.LAUNCHER
ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:26:13-73
	android:name
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml:26:23-70
uses-sdk
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
MERGED from [:react-native-async-storage_async-storage] D:\Sites\card-all\card-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-async-storage_async-storage] D:\Sites\card-all\card-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-community_cameraroll] D:\Sites\card-all\card-app\node_modules\@react-native-community\cameraroll\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-community_cameraroll] D:\Sites\card-all\card-app\node_modules\@react-native-community\cameraroll\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-community_masked-view] D:\Sites\card-all\card-app\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-community_masked-view] D:\Sites\card-all\card-app\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-community_slider] D:\Sites\card-all\card-app\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-community_slider] D:\Sites\card-all\card-app\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-picker_picker] D:\Sites\card-all\card-app\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-picker_picker] D:\Sites\card-all\card-app\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-gesture-handler] D:\Sites\card-all\card-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-gesture-handler] D:\Sites\card-all\card-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-pager-view] D:\Sites\card-all\card-app\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-pager-view] D:\Sites\card-all\card-app\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-safe-area-context] D:\Sites\card-all\card-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-safe-area-context] D:\Sites\card-all\card-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.react:react-native:0.66.2] C:\Users\<USER>\.gradle\caches\transforms-3\a21ceb2dee016b3bc178279e85108ab3\transformed\jetified-react-native-0.66.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.react:react-native:0.66.2] C:\Users\<USER>\.gradle\caches\transforms-3\a21ceb2dee016b3bc178279e85108ab3\transformed\jetified-react-native-0.66.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67dc7ae83599c50a17d333e48b750491\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67dc7ae83599c50a17d333e48b750491\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\8bb9561c589f5cd8f762c133b8ca94b9\transformed\appcompat-1.0.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\8bb9561c589f5cd8f762c133b8ca94b9\transformed\appcompat-1.0.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f14803a315f67746b9fb71068be927be\transformed\vectordrawable-animated-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f14803a315f67746b9fb71068be927be\transformed\vectordrawable-animated-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7fccd1f19787d63b03ada961c1fccf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7fccd1f19787d63b03ada961c1fccf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9341f36f0ce470d4b55127e33c2ce075\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9341f36f0ce470d4b55127e33c2ce075\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [org.webkit:android-jsc:r250230] C:\Users\<USER>\.gradle\caches\transforms-3\7873a5158addb5bf75ec8bc5226b9f2a\transformed\jetified-android-jsc-r250230\AndroidManifest.xml:7:5-9:41
MERGED from [org.webkit:android-jsc:r250230] C:\Users\<USER>\.gradle\caches\transforms-3\7873a5158addb5bf75ec8bc5226b9f2a\transformed\jetified-android-jsc-r250230\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fresco:fresco:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e3be5b59ff1001e620058abe4b7c8c9\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e3be5b59ff1001e620058abe4b7c8c9\transformed\jetified-fresco-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d576f05ff2510ef857a4a4bda541865\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d576f05ff2510ef857a4a4bda541865\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\da58426860718cf7cd9b6432bdc1e74c\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\da58426860718cf7cd9b6432bdc1e74c\transformed\jetified-drawee-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfcd70de602582dafbe585f453d01685\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfcd70de602582dafbe585f453d01685\transformed\jetified-nativeimagefilters-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29ad267efc1aa56118152dd96a23ba9e\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29ad267efc1aa56118152dd96a23ba9e\transformed\jetified-memory-type-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfe2fe1266f7b508fab117b793747328\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfe2fe1266f7b508fab117b793747328\transformed\jetified-memory-type-java-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\78fb03b70f57e7a893ea3d79f69454fd\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\78fb03b70f57e7a893ea3d79f69454fd\transformed\jetified-imagepipeline-native-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\9429e5f71025a78d24a47489563f8c07\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\9429e5f71025a78d24a47489563f8c07\transformed\jetified-memory-type-ashmem-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5526495f296c8eeaa211326006983f13\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5526495f296c8eeaa211326006983f13\transformed\jetified-imagepipeline-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c9b14e62e6566be253296541a1de4cb\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c9b14e62e6566be253296541a1de4cb\transformed\jetified-nativeimagetranscoder-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a48e403d00b0b0ddc92ec5c061189973\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a48e403d00b0b0ddc92ec5c061189973\transformed\jetified-imagepipeline-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\be9076cd4fc3ff6ad98aa6fb277b3148\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\be9076cd4fc3ff6ad98aa6fb277b3148\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:middleware:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bd3d214b93dcf428cfd42cc8814b2ed\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bd3d214b93dcf428cfd42cc8814b2ed\transformed\jetified-middleware-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\78eba43129e0d31bec1d131435c69dcf\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\78eba43129e0d31bec1d131435c69dcf\transformed\jetified-ui-common-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6d259317e4a56b55ba587f613fb48a2\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6d259317e4a56b55ba587f613fb48a2\transformed\jetified-soloader-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c346baed8905fc786ccf697aebc99f4\transformed\jetified-soloader-0.10.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c346baed8905fc786ccf697aebc99f4\transformed\jetified-soloader-0.10.1\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\db0f9dfc2cb9e58d20874cad38a46146\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\db0f9dfc2cb9e58d20874cad38a46146\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\e81715fd3e32bf93fae732ab0d910abb\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\e81715fd3e32bf93fae732ab0d910abb\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a19e52b53eb437c1a1e2beb909f23bc9\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a19e52b53eb437c1a1e2beb909f23bc9\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\be421ef5c9cb65032068b6cd3865fbe1\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\be421ef5c9cb65032068b6cd3865fbe1\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be91775d24cf865decfe7ed4d5eda66\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be91775d24cf865decfe7ed4d5eda66\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0c159f2eff86f9dd9484771b447df49\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0c159f2eff86f9dd9484771b447df49\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a91b8843137aadf2218f91eb02c85509\transformed\vectordrawable-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a91b8843137aadf2218f91eb02c85509\transformed\vectordrawable-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b761a92b9243332432b2b44741790500\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b761a92b9243332432b2b44741790500\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\866aad59c867e35bbba666bca4ca6ba8\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\866aad59c867e35bbba666bca4ca6ba8\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a26264be0cc8c8203d10219c4bd7f503\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a26264be0cc8c8203d10219c4bd7f503\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\945f679d5066ec5cea4c418de5c0d6c7\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\945f679d5066ec5cea4c418de5c0d6c7\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c40538da6b092d65991aa98428bae53c\transformed\jetified-play-services-tasks-17.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c40538da6b092d65991aa98428bae53c\transformed\jetified-play-services-tasks-17.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\86fd6928850ae509fbe2c21ac2bd3ae8\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\86fd6928850ae509fbe2c21ac2bd3ae8\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1b8018dc60702fe7502f85988cad1c2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1b8018dc60702fe7502f85988cad1c2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60d60c30d7e19309c125baad97767572\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60d60c30d7e19309c125baad97767572\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\09064782b8f9c0d066bcd2a3c8b04255\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\09064782b8f9c0d066bcd2a3c8b04255\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29cb4bc247544557fd2056ab72725c\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\be29cb4bc247544557fd2056ab72725c\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa7b39b5df705c5310dc54c9428ddb2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa7b39b5df705c5310dc54c9428ddb2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b133b9404a8c5da5d70c49b2ea9a8235\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b133b9404a8c5da5d70c49b2ea9a8235\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52776be0cd260004f7c3543526e82dde\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52776be0cd260004f7c3543526e82dde\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e78fd681fe13beced154580660d7b18a\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e78fd681fe13beced154580660d7b18a\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e66c2c7de81d8a94347e1fb5ab7df0ea\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e66c2c7de81d8a94347e1fb5ab7df0ea\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df29ea32d64ae57195ae50365029894\transformed\core-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b99f44f0bffbc60250191bc76f427b6\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b99f44f0bffbc60250191bc76f427b6\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43774a1f0f00442e6ac94bdb87895112\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43774a1f0f00442e6ac94bdb87895112\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\207c511f44f601b53084c2139b40be16\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\207c511f44f601b53084c2139b40be16\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1dbf150c803dbbe2473340e72042a7f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1dbf150c803dbbe2473340e72042a7f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b855ee38784b14b3dafd9751524a62d\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b855ee38784b14b3dafd9751524a62d\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6648bd14adbde7c30b9a5080863e29b0\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6648bd14adbde7c30b9a5080863e29b0\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef1333f51a0619e2cf1dea9c6bced6ac\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef1333f51a0619e2cf1dea9c6bced6ac\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52571f97fa6c48622e09021afd8f199b\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52571f97fa6c48622e09021afd8f199b\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\924aaf4fe951573165fc3f7358be4779\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\924aaf4fe951573165fc3f7358be4779\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f9e87f36900fbd3992a3b013c7da9cb\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f9e87f36900fbd3992a3b013c7da9cb\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:fbcore:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80cae4052e041bdf30fb0fa272e54409\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80cae4052e041bdf30fb0fa272e54409\transformed\jetified-fbcore-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\413d0ac1b6d1bcad01a3ffa6f288985d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\413d0ac1b6d1bcad01a3ffa6f288985d\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		ADDED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\Sites\card-all\card-app\android\app\src\main\AndroidManifest.xml
uses-feature#android.hardware.camera
ADDED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:11:5-13:36
	android:required
		ADDED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:13:9-33
	android:name
		ADDED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:12:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:14:5-16:36
	android:required
		ADDED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:16:9-33
	android:name
		ADDED from [:react-native-camera] D:\Sites\card-all\card-app\node_modules\react-native-camera\android\build\intermediates\merged_manifest\generalRelease\AndroidManifest.xml:15:9-57
uses-feature#android.hardware.nfc
ADDED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-13:36
	android:required
		ADDED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-33
	android:name
		ADDED from [:react-native-nfc-manager] D:\Sites\card-all\card-app\node_modules\react-native-nfc-manager\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:9-44
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-18:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:13-47
	android:authorities
		ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:13-64
	android:exported
		ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-17:63
	android:resource
		ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:17-60
	android:name
		ADDED from [:react-native-webview] D:\Sites\card-all\card-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:17-67
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf0a3b530326e5a0db06581731e98dd4\transformed\jetified-play-services-mlkit-barcode-scanning-16.2.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:16.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8117efe406658e934d36f1d7a562219f\transformed\jetified-play-services-mlkit-face-detection-16.2.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5db78a234ab47aefcf2b5b21d61f5ce5\transformed\jetified-play-services-mlkit-text-recognition-16.3.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:16.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2938b3a569aeabb9018b2aa9bf029884\transformed\jetified-vision-common-16.5.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d92bcde582c7cec9e65cec75467480f6\transformed\jetified-common-17.2.0\AndroidManifest.xml:21:17-120
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0e40bdc1ba615c480eeb4e284f3d2c5\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\deb31f6b92dbdb7e7d528a640e494dc6\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:23:9-26:75
	android:exported
		ADDED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-base:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\dae511b76fc5abe0b43b7dfd722fa90e\transformed\jetified-play-services-base-17.6.0\AndroidManifest.xml:24:13-79
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:17.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\060dff7644d80534b68ae20379930fc6\transformed\jetified-play-services-basement-17.6.0\AndroidManifest.xml:24:13-58
