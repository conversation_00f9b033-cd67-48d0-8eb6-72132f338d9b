<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/CardApp.iml" filepath="$PROJECT_DIR$/.idea/modules/CardApp.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/CardApp.app.iml" filepath="$PROJECT_DIR$/.idea/modules/app/CardApp.app.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/CardApp.app.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/app/CardApp.app.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/CardApp.app.main.iml" filepath="$PROJECT_DIR$/.idea/modules/app/CardApp.app.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/CardApp.app.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/app/CardApp.app.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.iml" filepath="$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1855115564/CardApp.react-native-async-storage_async-storage.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.iml" filepath="$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1719501244/CardApp.react-native-camera.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.iml" filepath="$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.main.iml" filepath="$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/467092441/CardApp.react-native-community_cameraroll.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.iml" filepath="$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-196145008/CardApp.react-native-community_masked-view.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.iml" filepath="$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-2012698600/CardApp.react-native-community_slider.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.iml" filepath="$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1799145913/CardApp.react-native-gesture-handler.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.iml" filepath="$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-145859444/CardApp.react-native-nfc-manager.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.iml" filepath="$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.main.iml" filepath="$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1666394126/CardApp.react-native-pager-view.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.iml" filepath="$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-504005314/CardApp.react-native-picker_picker.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.iml" filepath="$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1751000848/CardApp.react-native-safe-area-context.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.iml" filepath="$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-980483718/CardApp.react-native-webview.unitTest.iml" />
    </modules>
  </component>
</project>