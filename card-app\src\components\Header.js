/*
 * @Author: 高超
 * @Date: 2021-11-06 17:07:16
 * @LastEditTime: 2021-12-23 00:21:05
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/components/Header.js
 * love jiajia
 */
import React, { Component } from 'react';
import { View, StyleSheet, Image, Text, DeviceEventEmitter } from 'react-native';
import { Flex } from '@ant-design/react-native';

 const HeaderCom = (props) => {
    return (
      <View style={styles.header}>
        <Flex>
            <Flex.Item style={{ paddingLeft: 4, paddingRight: 4 }} onPressOut = { () => {
              props.navigation.goBack();
              DeviceEventEmitter.emit("nickBack", {});
            }}>
               <Image
              style={styles.tinyLogo}
              source={require('@static/back.png')}
              
            />
            </Flex.Item>
            <Flex.Item>
              <Text style={styles.title}>{props.name}</Text>
            </Flex.Item>
            <Flex.Item>
            </Flex.Item>
          </Flex>
      </View>
    )
}
const styles = StyleSheet.create({
  header: {
    width: '100%',
    backgroundColor:  '#fff',
    height: 55,
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#cccccc',
    justifyContent:'center'
  },  
  tinyLogo : {
    width: 30,
    height: 30,
    marginLeft: 10
  },
  title : {
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center'
  }
});

export default HeaderCom