<component name="libraryTable">
  <library name="Gradle: com.facebook.fresco:drawee:2.5.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/da58426860718cf7cd9b6432bdc1e74c/transformed/jetified-drawee-2.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/da58426860718cf7cd9b6432bdc1e74c/transformed/jetified-drawee-2.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/da58426860718cf7cd9b6432bdc1e74c/transformed/jetified-drawee-2.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/drawee/2.5.0/1130f0faf1622f3f1ece8dc453b6ea08f92d9170/drawee-2.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/drawee/2.5.0/d73151e9d1ad658ffb91f09f919ee1239411fbe9/drawee-2.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>