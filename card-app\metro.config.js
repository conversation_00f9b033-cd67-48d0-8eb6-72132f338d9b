/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const crypto = require('crypto');

// Fix for Node.js 17+ OpenSSL issue with Metro bundler
// This only affects development/build time, not runtime bundle content
const originalCreateHash = crypto.createHash;
crypto.createHash = (algorithm) => {
  // Metro cache uses MD4 which is deprecated in Node.js 17+
  // Replace with MD5 for cache key generation only
  if (algorithm === 'md4') {
    return originalCreateHash('md5');
  }
  return originalCreateHash(algorithm);
};

module.exports = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};
