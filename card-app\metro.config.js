/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const crypto = require('crypto');

// Fix for Node.js 17+ OpenSSL issue
const originalCreateHash = crypto.createHash;
crypto.createHash = (algorithm) => {
  if (algorithm === 'md4') {
    return originalCreateHash('md5');
  }
  return originalCreateHash(algorithm);
};

module.exports = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};
