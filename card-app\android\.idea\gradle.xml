<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-async-storage/async-storage/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/cameraroll/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/masked-view/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-community/slider/android" />
            <option value="$PROJECT_DIR$/../node_modules/@react-native-picker/picker/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-camera/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-gesture-handler/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-nfc-manager/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-pager-view/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-safe-area-context/android" />
            <option value="$PROJECT_DIR$/../node_modules/react-native-webview/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>