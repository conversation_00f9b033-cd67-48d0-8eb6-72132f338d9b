#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/uglify-es@3.3.9/node_modules/uglify-es/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/uglify-es@3.3.9/node_modules/uglify-es/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/uglify-es@3.3.9/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/uglify-es@3.3.9/node_modules/uglify-es/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/uglify-es@3.3.9/node_modules/uglify-es/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/uglify-es@3.3.9/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../uglify-es/bin/uglifyjs" "$@"
else
  exec node  "$basedir/../uglify-es/bin/uglifyjs" "$@"
fi
