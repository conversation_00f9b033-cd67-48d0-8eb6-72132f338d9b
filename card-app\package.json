{"name": "CardApp", "version": "0.0.1", "private": true, "scripts": {"android": "NODE_OPTIONS='--openssl-legacy-provider' react-native run-android", "ios": "NODE_OPTIONS='--openssl-legacy-provider' react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@ant-design/icons-react-native": "^2.3.2", "@ant-design/react-native": "^4.2.0", "@react-native-async-storage/async-storage": "^1.15.11", "@react-native-community/cameraroll": "^4.1.2", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/segmented-control": "^2.2.2", "@react-native-community/slider": "^4.4.2", "@react-native-picker/picker": "^2.4.8", "moment": "^2.29.1", "ms": "^2.1.3", "react": "17.0.2", "react-native": "0.66.2", "react-native-camera": "^4.2.1", "react-native-gesture-handler": "^1.10.3", "react-native-nfc-manager": "^3.11.0", "react-native-pager-view": "^5.4.8", "react-native-safe-area-context": "^3.3.2", "react-native-webview": "^11.14.2", "react-navigation": "^4.4.4", "react-navigation-stack": "^2.10.4"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/runtime": "^7.16.0", "@react-native-community/eslint-config": "^3.0.1", "babel-jest": "^27.3.1", "babel-plugin-import": "^1.13.3", "babel-plugin-module-resolver": "^4.1.0", "eslint": "^8.2.0", "jest": "^27.3.1", "metro-react-native-babel-preset": "^0.66.2", "react-test-renderer": "17.0.2"}, "jest": {"preset": "react-native"}}