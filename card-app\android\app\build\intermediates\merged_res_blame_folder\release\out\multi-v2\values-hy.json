{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-hy\\values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,546,678,784,997,1131,1284,1422,1648,1754,1953,2087,2308,2500,2591,2684", "endColumns": "103,196,131,105,212,133,152,137,225,105,198,133,220,191,90,92,106", "endOffsets": "348,545,677,783,996,1130,1283,1421,1647,1753,1952,2086,2307,2499,2590,2683,2790"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2802,2910,3107,3243,3353,3566,3704,3861,4213,4439,4549,4748,4886,5107,5303,5398,5495", "endColumns": "107,196,135,109,212,137,156,141,225,109,198,137,220,195,94,96,110", "endOffsets": "2905,3102,3238,3348,3561,3699,3856,3998,4434,4544,4743,4881,5102,5298,5393,5490,5601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "209", "endOffsets": "456"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "4003", "endColumns": "209", "endOffsets": "4208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5688", "endColumns": "100", "endOffsets": "5784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,983,1076,1172,1266,1367,1460,1555,1649,1740,1831,1916,2023,2130,2229,2339,2446,2546,2703,2802", "endColumns": "102,100,109,88,105,114,81,80,90,92,95,93,100,92,94,93,90,90,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,978,1071,1167,1261,1362,1455,1550,1644,1735,1826,1911,2018,2125,2224,2334,2441,2541,2698,2797,2879"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,983,1076,1172,1266,1367,1460,1555,1649,1740,1831,1916,2023,2130,2229,2339,2446,2546,2703,5606", "endColumns": "102,100,109,88,105,114,81,80,90,92,95,93,100,92,94,93,90,90,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,978,1071,1167,1261,1362,1455,1550,1644,1735,1826,1911,2018,2125,2224,2334,2441,2541,2698,2797,5683"}}]}]}