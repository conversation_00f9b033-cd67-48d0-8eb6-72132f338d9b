<component name="libraryTable">
  <library name="Gradle: com.facebook.fresco:nativeimagetranscoder:2.5.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/9c9b14e62e6566be253296541a1de4cb/transformed/jetified-nativeimagetranscoder-2.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9c9b14e62e6566be253296541a1de4cb/transformed/jetified-nativeimagetranscoder-2.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/nativeimagetranscoder/2.5.0/3b06032fc7ddb44fc9d2a3e72c2cf8af34fa4e31/nativeimagetranscoder-2.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/nativeimagetranscoder/2.5.0/403fdb5dc1f2676149ec8a0131c2e1edff6723f6/nativeimagetranscoder-2.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>