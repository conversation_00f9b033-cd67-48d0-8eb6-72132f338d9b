{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-land\\values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}]}