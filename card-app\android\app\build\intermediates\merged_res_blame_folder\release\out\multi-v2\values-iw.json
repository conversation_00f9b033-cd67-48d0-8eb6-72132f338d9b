{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-iw\\values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5392", "endColumns": "100", "endOffsets": "5488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,346,532,655,757,955,1078,1216,1344,1539,1641,1817,1938,2131,2302,2388,2474", "endColumns": "100,185,122,101,197,122,137,127,194,101,175,120,192,170,85,85,105", "endOffsets": "345,531,654,756,954,1077,1215,1343,1538,1640,1816,1937,2130,2301,2387,2473,2579"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2751,2856,3042,3169,3275,3473,3600,3742,4051,4246,4352,4528,4653,4846,5021,5111,5201", "endColumns": "104,185,126,105,197,126,141,131,194,105,175,124,192,174,89,89,109", "endOffsets": "2851,3037,3164,3270,3468,3595,3737,3869,4241,4347,4523,4648,4841,5016,5106,5196,5306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "176", "endOffsets": "423"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3874", "endColumns": "176", "endOffsets": "4046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,961,1055,1151,1245,1346,1439,1534,1631,1722,1814,1895,1997,2101,2199,2302,2403,2503,2655,2751", "endColumns": "103,98,107,83,99,113,77,77,90,93,95,93,100,92,94,96,90,91,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,956,1050,1146,1240,1341,1434,1529,1626,1717,1809,1890,1992,2096,2194,2297,2398,2498,2650,2746,2827"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,961,1055,1151,1245,1346,1439,1534,1631,1722,1814,1895,1997,2101,2199,2302,2403,2503,2655,5311", "endColumns": "103,98,107,83,99,113,77,77,90,93,95,93,100,92,94,96,90,91,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,956,1050,1146,1240,1341,1434,1529,1626,1717,1809,1890,1992,2096,2194,2297,2398,2498,2650,2746,5387"}}]}]}