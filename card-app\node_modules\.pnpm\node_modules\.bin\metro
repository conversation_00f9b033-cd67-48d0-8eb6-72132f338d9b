#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro@0.66.2/node_modules/metro/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro@0.66.2/node_modules/metro/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro@0.66.2/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro@0.66.2/node_modules/metro/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro@0.66.2/node_modules/metro/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/metro@0.66.2/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../metro/src/cli.js" "$@"
else
  exec node  "$basedir/../metro/src/cli.js" "$@"
fi
