/*
 * @Author: 高超
 * @Date: 2021-12-22 11:32:25
 * @LastEditTime: 2021-12-22 21:45:27
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/src/pages/web/Web.js
 * love jiajia
 */
import React, { Component } from 'react';
import { WebView } from 'react-native-webview';
import Tools from '@utils/tools';

export default class MyWebComponent extends Component {

  static navigationOptions = {
    title : "信息"
  }

  state ={
    url: ''
  }

  componentDidMount() {
    this.setState({
      url: `${Tools.baseUrl}${this.props.navigation.state.params.url}`
    })
  }

  render() {
    return <WebView source={{ uri: this.state.url }} />;
  }
}