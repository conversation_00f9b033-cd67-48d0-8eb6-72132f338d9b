@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules\@react-native-community\cli\build\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules\@react-native-community\cli\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules\@react-native-community\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules\@react-native-community\cli\build\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules\@react-native-community\cli\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules\@react-native-community\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\@react-native-community+cli_9d65ed3a17061c8b239e68349bbcfe4c\node_modules;D:\Sites\card-all\card-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@react-native-community\cli\build\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@react-native-community\cli\build\bin.js" %*
)
