<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/19dbf037054f995da5dedc6be291ab0b/transformed/lifecycle-viewmodel-2.3.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/19dbf037054f995da5dedc6be291ab0b/transformed/lifecycle-viewmodel-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/19dbf037054f995da5dedc6be291ab0b/transformed/lifecycle-viewmodel-2.3.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-viewmodel/2.3.1/55d6fa3541ca02167b0bd62a16fbdaec2a71622/lifecycle-viewmodel-2.3.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>