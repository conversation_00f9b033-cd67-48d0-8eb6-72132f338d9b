<component name="libraryTable">
  <library name="Gradle: androidx.fragment:fragment:1.3.6@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/6002c4bad2084e99b3580f0d613eae70/transformed/fragment-1.3.6/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/6002c4bad2084e99b3580f0d613eae70/transformed/fragment-1.3.6/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6002c4bad2084e99b3580f0d613eae70/transformed/fragment-1.3.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6002c4bad2084e99b3580f0d613eae70/transformed/fragment-1.3.6/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.fragment/fragment/1.3.6/25ece06338d39da1fdc9d8488aa57b5014866918/fragment-1.3.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>