/*
 * @Author: 高超
 * @Date: 2021-11-07 10:36:09
 * @LastEditTime: 2021-12-22 18:14:16
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/App.js
 * love jiajia
 */
import React from 'react';
import { createAppContainer } from 'react-navigation';
import { createStackNavigator } from 'react-navigation-stack';
import AppHome from '@pages/home/<USER>';
import HistoryList from '@pages/histroy/List';
import BookList from '@pages/histroy/Book';
import MaCode from '@pages/code/MaCode';
import KeyCode from '@pages/code/KeyCode';
import UserInfo from '@pages/user/UserInfo';
import Login from '@pages/login/Login';
import Web from '@pages/web/Web';

class IndexScreen extends React.Component {
  static navigationOptions = {
    headerShown : false
  };
  render() { return (null) }
}

const AppNavigator = createStackNavigator({
    Index: {
      screen: IndexScreen
    },
    Home: {
      screen : AppHome
    },
    History: {
      screen: HistoryList
    },
    Book: {
      screen: BookList
    },
    MaLook: {
      screen: MaCode
    },
    KeyLook: {
      screen: KeyCode
    },
    UserInfo: {
      screen: UserInfo
    },
    Login: {
      screen: Login
    },
    Web: {
      screen: Web
    }
  }, 
  {
    initialRouteName: 'Home',
});

export default createAppContainer(AppNavigator);