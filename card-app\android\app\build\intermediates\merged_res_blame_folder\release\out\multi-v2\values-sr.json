{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-sr\\values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "188", "endOffsets": "435"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3972", "endColumns": "188", "endOffsets": "4156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,537,661,769,982,1107,1245,1372,1580,1685,1876,2001,2209,2377,2471,2562", "endColumns": "103,187,123,107,212,124,137,126,207,104,190,124,207,167,93,90,104", "endOffsets": "348,536,660,768,981,1106,1244,1371,1579,1684,1875,2000,2208,2376,2470,2561,2666"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2821,2929,3117,3245,3357,3570,3699,3841,4161,4369,4478,4669,4798,5006,5178,5276,5371", "endColumns": "107,187,127,111,212,128,141,130,207,108,190,128,207,171,97,94,108", "endOffsets": "2924,3112,3240,3352,3565,3694,3836,3967,4364,4473,4664,4793,5001,5173,5271,5366,5475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,423,509,613,731,812,892,983,1076,1172,1266,1367,1460,1555,1660,1751,1842,1930,2035,2143,2244,2348,2456,2557,2724,2821", "endColumns": "108,102,105,85,103,117,80,79,90,92,95,93,100,92,94,104,90,90,87,104,107,100,103,107,100,166,96,83", "endOffsets": "209,312,418,504,608,726,807,887,978,1071,1167,1261,1362,1455,1550,1655,1746,1837,1925,2030,2138,2239,2343,2451,2552,2719,2816,2900"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,423,509,613,731,812,892,983,1076,1172,1266,1367,1460,1555,1660,1751,1842,1930,2035,2143,2244,2348,2456,2557,2724,5480", "endColumns": "108,102,105,85,103,117,80,79,90,92,95,93,100,92,94,104,90,90,87,104,107,100,103,107,100,166,96,83", "endOffsets": "209,312,418,504,608,726,807,887,978,1071,1167,1261,1362,1455,1550,1655,1746,1837,1925,2030,2138,2239,2343,2451,2552,2719,2816,5559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5564", "endColumns": "100", "endOffsets": "5660"}}]}]}