<component name="libraryTable">
  <library name="Gradle: androidx.viewpager2:viewpager2:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a19e52b53eb437c1a1e2beb909f23bc9/transformed/jetified-viewpager2-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a19e52b53eb437c1a1e2beb909f23bc9/transformed/jetified-viewpager2-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a19e52b53eb437c1a1e2beb909f23bc9/transformed/jetified-viewpager2-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a19e52b53eb437c1a1e2beb909f23bc9/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.viewpager2/viewpager2/1.0.0/3c3569044e6969f1ee5c3aa03b08e6717a2d782f/viewpager2-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>