<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db0654bf-7f4d-48e4-b7a9-99767bafb91e" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/.github/workflows/nodejs.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/.github/workflows/nodejs.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/.travis.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/.travis.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/appveyor.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/appveyor.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/asset-manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/asset-manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/brand/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/brand/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/brand/index/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/brand/index/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/brand/sort/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/brand/sort/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/card/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/card/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/card/index/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/card/index/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/card/sort/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/card/sort/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/min/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/min/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/min/topic/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/min/topic/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/min/turn/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/min/turn/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/news/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/news/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/societyList/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/societyList/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/user/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/user/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/user/login/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/user/login/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/welcome/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-server/assets/webConfigCenter/webConfigCenter/welcome/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-weapp/config/dev.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-weapp/config/dev.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-weapp/config/prod.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-weapp/config/prod.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-weapp/project.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-weapp/project.config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../card-weapp/src/utils/api.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../card-weapp/src/utils/api.config.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[C:\Users\<USER>\.android\avd\Nexus_S_API_30.avd]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="ProjectId" id="2IAJzupwSl7SwT0b8FQmXzdyCe6" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="RunOnceActivity.cidr.known.project.marker" value="true" />
    <property name="cidr.known.project.marker" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="http.proxy" />
    <property name="show.do.not.copy.http.proxy.settings.to.gradle" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="CardApp.app" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="INSPECTION_WITHOUT_ACTIVITY_RESTART" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
      </Hybrid>
      <Java />
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="db0654bf-7f4d-48e4-b7a9-99767bafb91e" name="Changes" comment="" />
      <created>1669618723439</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1669618723439</updated>
    </task>
    <servers />
  </component>
</project>