<component name="libraryTable">
  <library name="Gradle: com.facebook.fresco:ui-common:2.5.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/78eba43129e0d31bec1d131435c69dcf/transformed/jetified-ui-common-2.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/78eba43129e0d31bec1d131435c69dcf/transformed/jetified-ui-common-2.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/ui-common/2.5.0/66a5f47db887dae67f6b32117fd90965e6406005/ui-common-2.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/ui-common/2.5.0/a646b9485662da281a6374e140a85cdd1c8a3b36/ui-common-2.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>