#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/sane@4.1.0/node_modules/sane/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/sane@4.1.0/node_modules/sane/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/sane@4.1.0/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/sane@4.1.0/node_modules/sane/src/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/sane@4.1.0/node_modules/sane/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/sane@4.1.0/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../sane/src/cli.js" "$@"
else
  exec node  "$basedir/../sane/src/cli.js" "$@"
fi
