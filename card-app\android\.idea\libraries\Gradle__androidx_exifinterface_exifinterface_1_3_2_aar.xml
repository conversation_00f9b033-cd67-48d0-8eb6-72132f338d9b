<component name="libraryTable">
  <library name="Gradle: androidx.exifinterface:exifinterface:1.3.2@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e81715fd3e32bf93fae732ab0d910abb/transformed/exifinterface-1.3.2/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e81715fd3e32bf93fae732ab0d910abb/transformed/exifinterface-1.3.2/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e81715fd3e32bf93fae732ab0d910abb/transformed/exifinterface-1.3.2/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.exifinterface/exifinterface/1.3.2/1bd0ec4a0281e75ef08c8317c92f800e48b0fd19/exifinterface-1.3.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>