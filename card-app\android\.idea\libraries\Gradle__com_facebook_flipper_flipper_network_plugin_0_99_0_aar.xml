<component name="libraryTable">
  <library name="Gradle: com.facebook.flipper:flipper-network-plugin:0.99.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/2c29b736c8f2a9d82fade7115b471d70/transformed/jetified-flipper-network-plugin-0.99.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2c29b736c8f2a9d82fade7115b471d70/transformed/jetified-flipper-network-plugin-0.99.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.flipper/flipper-network-plugin/0.99.0/337064676884b6247c3e7b926cc2e4b16bbe5eb6/flipper-network-plugin-0.99.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.flipper/flipper-network-plugin/0.99.0/577fbc35ec5243ce3d011092d95e679b1372298a/flipper-network-plugin-0.99.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>