{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\android\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-it\\values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "194", "endOffsets": "441"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3980", "endColumns": "194", "endOffsets": "4170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,314,423,507,616,741,818,894,986,1080,1174,1268,1370,1464,1561,1667,1759,1851,1932,2038,2146,2244,2348,2453,2560,2723,2823", "endColumns": "108,99,108,83,108,124,76,75,91,93,93,93,101,93,96,105,91,91,80,105,107,97,103,104,106,162,99,82", "endOffsets": "209,309,418,502,611,736,813,889,981,1075,1169,1263,1365,1459,1556,1662,1754,1846,1927,2033,2141,2239,2343,2448,2555,2718,2818,2901"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,314,423,507,616,741,818,894,986,1080,1174,1268,1370,1464,1561,1667,1759,1851,1932,2038,2146,2244,2348,2453,2560,2723,5534", "endColumns": "108,99,108,83,108,124,76,75,91,93,93,93,101,93,96,105,91,91,80,105,107,97,103,104,106,162,99,82", "endOffsets": "209,309,418,502,611,736,813,889,981,1075,1169,1263,1365,1459,1556,1662,1754,1846,1927,2033,2141,2239,2343,2448,2555,2718,2818,5612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,348,531,654,760,977,1103,1249,1378,1594,1699,1884,2009,2224,2431,2524,2611", "endColumns": "102,182,122,105,216,125,145,128,215,104,184,124,214,206,92,86,101", "endOffsets": "347,530,653,759,976,1102,1248,1377,1593,1698,1883,2008,2223,2430,2523,2610,2712"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2823,2930,3113,3240,3350,3567,3697,3847,4175,4391,4500,4685,4814,5029,5240,5337,5428", "endColumns": "106,182,126,109,216,129,149,132,215,108,184,128,214,210,96,90,105", "endOffsets": "2925,3108,3235,3345,3562,3692,3842,3975,4386,4495,4680,4809,5024,5235,5332,5423,5529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6df29ea32d64ae57195ae50365029894\\transformed\\core-1.2.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5617", "endColumns": "100", "endOffsets": "5713"}}]}]}