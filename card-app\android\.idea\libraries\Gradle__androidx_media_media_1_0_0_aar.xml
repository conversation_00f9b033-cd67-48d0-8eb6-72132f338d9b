<component name="libraryTable">
  <library name="Gradle: androidx.media:media:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/945f679d5066ec5cea4c418de5c0d6c7/transformed/media-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/945f679d5066ec5cea4c418de5c0d6c7/transformed/media-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/945f679d5066ec5cea4c418de5c0d6c7/transformed/media-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/945f679d5066ec5cea4c418de5c0d6c7/transformed/media-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.media/media/1.0.0/5faf25be15e16e27275f5c7e04e15e944ebccea0/media-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>