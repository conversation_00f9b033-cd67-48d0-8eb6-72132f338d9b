<component name="libraryTable">
  <library name="Gradle: com.facebook.fresco:memory-type-java:2.5.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/cfe2fe1266f7b508fab117b793747328/transformed/jetified-memory-type-java-2.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/cfe2fe1266f7b508fab117b793747328/transformed/jetified-memory-type-java-2.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/memory-type-java/2.5.0/2a9f37fef921ddb1fef3dc95ce06879241d3bd1a/memory-type-java-2.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/memory-type-java/2.5.0/ebf3374790d59f7eecf2326a6fee0dadf5d51ee5/memory-type-java-2.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>