<component name="libraryTable">
  <library name="Gradle: com.facebook.fresco:memory-type-ashmem:2.5.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/9429e5f71025a78d24a47489563f8c07/transformed/jetified-memory-type-ashmem-2.5.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9429e5f71025a78d24a47489563f8c07/transformed/jetified-memory-type-ashmem-2.5.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/memory-type-ashmem/2.5.0/c4472e14c3f2865fc972efe4ed9055cf34a53e5e/memory-type-ashmem-2.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.fresco/memory-type-ashmem/2.5.0/f0a2d07ff418c927b5f2ccfa269f07a4ef0edf82/memory-type-ashmem-2.5.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>