#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jscodeshift@0.11.0_@babel+p_a86564bbd600e979fc1c54734aac11a9/node_modules/jscodeshift/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jscodeshift@0.11.0_@babel+p_a86564bbd600e979fc1c54734aac11a9/node_modules/jscodeshift/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jscodeshift@0.11.0_@babel+p_a86564bbd600e979fc1c54734aac11a9/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jscodeshift@0.11.0_@babel+p_a86564bbd600e979fc1c54734aac11a9/node_modules/jscodeshift/bin/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jscodeshift@0.11.0_@babel+p_a86564bbd600e979fc1c54734aac11a9/node_modules/jscodeshift/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/jscodeshift@0.11.0_@babel+p_a86564bbd600e979fc1c54734aac11a9/node_modules:/mnt/d/Sites/card-all/card-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jscodeshift/bin/jscodeshift.js" "$@"
else
  exec node  "$basedir/../jscodeshift/bin/jscodeshift.js" "$@"
fi
