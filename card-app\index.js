/*
 * @Author: 高超
 * @Date: 2021-11-13 17:15:37
 * @LastEditTime: 2021-12-23 00:28:00
 * @FilePath: /card-server/Users/<USER>/oneCard/CardApp/index.js
 * love jiajia
 */

import { AppReg<PERSON>ry, ToastAndroid, DeviceEventEmitter } from 'react-native';
import NfcManager, { NfcEvents } from 'react-native-nfc-manager';
import Tools from '@utils/tools'
import App from './App';
import {name as appName} from './app.json';

const readNdef = async() => {
    try {
          const cleanUp = () => {
            NfcManager.setEventListener(NfcEvents.DiscoverTag, null);
            NfcManager.setEventListener(NfcEvents.SessionClosed, null);
          };
          return new Promise((resolve) => {
            let tagFound = null;
            NfcManager.setEventListener(NfcEvents.DiscoverTag, (tag) => {
              tagFound = tag;
              DeviceEventEmitter.emit('nfc',tagFound.id)
              resolve(tagFound);
              //xNfcManager.unregisterTagEvent().catch(() => 0);
            });
        
            NfcManager.setEventListener(NfcEvents.SessionClosed, () => {
              if (!tagFound) {
                resolve();
              }
            });
            NfcManager.registerTagEvent();
          });
    } catch (e) {
        //console.log(e)
    }
}

(async () => {
    const num = await Tools.getData('num')
    if (num === null) await Tools.setData('num', 0)
    const nfcIsEnabled = await NfcManager.isEnabled()
    await Tools.setData('NFC', nfcIsEnabled)
    if (nfcIsEnabled) {
        try {
            await NfcManager.start()
            await readNdef()
        } catch(e) {
            //console.log(e)
        }
    } else {
        ToastAndroid.show('该设备不支持NFC（贴卡验证）', 1000) 
    }
})()

AppRegistry.registerComponent(appName, () => App);
