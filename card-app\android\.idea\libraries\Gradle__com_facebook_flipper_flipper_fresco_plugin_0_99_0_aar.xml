<component name="libraryTable">
  <library name="Gradle: com.facebook.flipper:flipper-fresco-plugin:0.99.0@aar">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e3a733608d857c466939da27af54ba77/transformed/jetified-flipper-fresco-plugin-0.99.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e3a733608d857c466939da27af54ba77/transformed/jetified-flipper-fresco-plugin-0.99.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.flipper/flipper-fresco-plugin/0.99.0/69ba0e183e4bb0ab51bbddc58e36784cc0bcb5cc/flipper-fresco-plugin-0.99.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.facebook.flipper/flipper-fresco-plugin/0.99.0/4c416ce2cbe43a9473238b99bfe7d20527140baa/flipper-fresco-plugin-0.99.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>